@echo off
title Periodic Table Quest - Exhibition Auto Launcher
color 0A

echo.
echo ================================================================
echo    🧪 PERIODIC TABLE QUEST - EXHIBITION AUTO LAUNCHER 🧪
echo ================================================================
echo.
echo ✨ Automatically launching your enhanced exhibition project...
echo 🎨 All visual enhancements will be active!
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found! Please install Python first.
    echo.
    pause
    exit /b 1
)

REM Check if the main files exist
if not exist "periodic_quest_fixed.py" (
    if not exist "periodic_quest_working.py" (
        echo ❌ Exhibition files not found in current directory!
        echo    Make sure you're running this from the project folder.
        echo.
        pause
        exit /b 1
    )
)

echo ✅ Python found and exhibition files detected!
echo.
echo 🚀 Launching enhanced exhibition in 3 seconds...
echo    (Press Ctrl+C to cancel)
echo.

timeout /t 3 /nobreak >nul

echo 🎉 LAUNCHING EXHIBITION!
echo.

REM Launch the fixed version first
python periodic_quest_fixed.py

REM If that fails, try the auto-launcher
if errorlevel 1 (
    echo.
    echo ⚠️ Fixed version failed, trying auto-launcher...
    python auto_launch.py
)

REM If that fails, try the working version directly
if errorlevel 1 (
    echo.
    echo ⚠️ Auto-launcher failed, trying working version...
    python periodic_quest_working.py
)

REM If that also fails, try the exhibition launcher
if errorlevel 1 (
    echo.
    echo ⚠️ Working version failed, trying exhibition launcher...
    python launch_exhibition.py
)

echo.
echo 👋 Exhibition launcher completed.
echo.
pause
