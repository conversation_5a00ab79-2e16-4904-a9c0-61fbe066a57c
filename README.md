# 🧪 Periodic Table Quest

A Python-based quiz game to test your knowledge of the periodic table of elements!

## 📋 Description

Periodic Table Quest is an educational game that challenges players on their knowledge of chemical elements. The game features different difficulty levels, a scoring system, and a timer to make learning about chemistry fun and engaging.

## ✨ Features

- **Multiple Difficulty Levels**: Choose between Easy (elements 1-20), Medium (elements 1-40), or Hard (elements 1-60)
- **Various Question Types**: Questions about element symbols, names, atomic numbers, and element groups
- **Scoring System**: Earn points based on how quickly you answer and track your high scores
- **Timer**: Each question has a countdown timer to add excitement
- **Hint System**: Get hints if you're stuck (but it will reduce your points!)
- **Fun Facts**: Learn interesting facts about elements after each question
- **Two Versions**: Play in the command line or with a graphical user interface

## 🚀 How to Play

### Command Line Version

1. Run `python periodic_quest.py`
2. Select a difficulty level
3. Answer the questions by typing your answer
4. Type 'hint' if you need a hint
5. Try to answer quickly for more points!

### GUI Version

1. Run `python periodic_quest_gui.py`
2. Click on a difficulty level
3. Type your answer in the text box
4. Click "Get Hint" if you need help
5. Click "Submit Answer" or press Enter to submit
6. Try to beat your high score!

## 📚 Question Types

- **Symbol to Name**: "What element has the symbol 'O'?"
- **Name to Symbol**: "What is the symbol for Oxygen?"
- **Number to Element**: "What element has atomic number 8?"
- **Element to Number**: "What is the atomic number of Oxygen?"
- **Group to Element**: "Name an element in the Noble Gases group."

## 🔧 Requirements

- Python 3.6 or higher
- Tkinter (for the GUI version, usually comes with Python)

## 📝 Files

- `periodic_data.py`: Contains the periodic table data
- `periodic_quest.py`: Command line version of the game
- `periodic_quest_gui.py`: GUI version of the game
- `high_scores.json`: Stores high scores (created automatically)

## 🎮 Tips for Success

- Learn the first 20 elements well before moving to higher difficulties
- Memorize both element names and their symbols
- Pay attention to the fun facts to learn more about each element
- Try to answer quickly for maximum points

Enjoy learning about the periodic table while having fun!
