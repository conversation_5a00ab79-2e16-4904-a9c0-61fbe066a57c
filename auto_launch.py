"""
Auto-Launch Script for Periodic Table Quest Exhibition
This script automatically opens the final enhanced project every time it's run.
Perfect for exhibitions - just double-click and your enhanced game opens!
"""

import tkinter as tk
from tkinter import messagebox
import subprocess
import sys
import os
import threading
import time

def auto_launch_exhibition():
    """Automatically launch the best exhibition version."""
    print("🧪 AUTO-LAUNCHING PERIODIC TABLE QUEST EXHIBITION")
    print("=" * 60)
    print("🚀 Starting enhanced exhibition version automatically...")
    print("✨ All visual enhancements will be active!")
    print("=" * 60)
    
    try:
        # Launch the working exhibition version automatically
        subprocess.Popen([sys.executable, "periodic_quest_working.py"])
        print("✅ Exhibition version launched successfully!")
        
        # Wait a moment for the window to open
        time.sleep(2)
        
        # Also show a quick status message
        root = tk.Tk()
        root.withdraw()  # Hide the main window
        
        messagebox.showinfo("🎉 Exhibition Launched!", 
                          "🧪 PERIODIC TABLE QUEST EXHIBITION\n\n" +
                          "✅ Enhanced version is now running!\n\n" +
                          "🎨 Features Active:\n" +
                          "• Animated floating atoms\n" +
                          "• Glowing interactive buttons\n" +
                          "• Color-changing title\n" +
                          "• Visual click effects\n" +
                          "• Professional laboratory theme\n\n" +
                          "🎯 Perfect for your exhibition!\n" +
                          "The enhanced game window should be open now.")
        
        root.destroy()
        
    except Exception as e:
        print(f"❌ Error launching exhibition: {e}")
        
        # Show error and fallback options
        root = tk.Tk()
        root.withdraw()
        
        result = messagebox.askyesno("Launch Error", 
                                   f"Could not auto-launch exhibition version.\n\n" +
                                   f"Error: {e}\n\n" +
                                   "Would you like to open the exhibition launcher instead?")
        
        if result:
            try:
                subprocess.Popen([sys.executable, "launch_exhibition.py"])
                print("✅ Exhibition launcher opened as fallback")
            except Exception as e2:
                messagebox.showerror("Error", f"Could not open launcher: {e2}")
        
        root.destroy()

def create_desktop_shortcut():
    """Create a desktop shortcut for easy access."""
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        path = os.path.join(desktop, "Periodic Table Quest Exhibition.lnk")
        target = os.path.join(os.getcwd(), "auto_launch.py")
        wDir = os.getcwd()
        icon = target
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = sys.executable
        shortcut.Arguments = f'"{target}"'
        shortcut.WorkingDirectory = wDir
        shortcut.IconLocation = icon
        shortcut.save()
        
        print("✅ Desktop shortcut created!")
        return True
        
    except ImportError:
        print("💡 To create desktop shortcuts, install: pip install winshell pywin32")
        return False
    except Exception as e:
        print(f"❌ Could not create desktop shortcut: {e}")
        return False

def check_files():
    """Check if all required files exist."""
    required_files = [
        "periodic_quest_working.py",
        "periodic_quest_gui.py",
        "periodic_data.py",
        "launch_exhibition.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Missing required files: {missing_files}")
        return False
    
    print("✅ All required files found!")
    return True

def main():
    """Main auto-launch function."""
    print("🧪 PERIODIC TABLE QUEST - AUTO LAUNCHER")
    print("=" * 50)
    print("🎯 This script automatically opens your enhanced exhibition!")
    print()
    
    # Check if files exist
    if not check_files():
        input("❌ Missing files. Press Enter to exit...")
        return
    
    # Check if assets exist
    assets_exist = (
        os.path.exists("assets/images/lab_bg.jpg") and
        os.path.exists("assets/sounds/click.wav")
    )
    
    if assets_exist:
        print("✅ Assets found - Full enhanced experience available!")
    else:
        print("⚠️ Some assets missing - Basic enhanced experience available")
    
    print()
    print("🚀 Auto-launching in 3 seconds...")
    print("   (Press Ctrl+C to cancel)")
    
    try:
        # Countdown
        for i in range(3, 0, -1):
            print(f"   {i}...")
            time.sleep(1)
        
        print("🎉 LAUNCHING!")
        auto_launch_exhibition()
        
    except KeyboardInterrupt:
        print("\n❌ Launch cancelled by user")
        
        # Ask if they want the launcher instead
        try:
            choice = input("\n🤔 Open exhibition launcher instead? (y/n): ").lower()
            if choice in ['y', 'yes']:
                subprocess.Popen([sys.executable, "launch_exhibition.py"])
                print("✅ Exhibition launcher opened!")
        except:
            pass

if __name__ == "__main__":
    main()
    
    # Keep the console open briefly to show any messages
    try:
        time.sleep(2)
    except:
        pass
