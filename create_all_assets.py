"""
Complete asset creation script for Periodic Table Quest Exhibition.
This creates ALL required assets to make the enhanced version fully functional.
"""

import os
import json
from PIL import Image, ImageDraw, ImageFont
import pygame
import numpy as np
import math

def create_directories():
    """Create all required directories."""
    directories = [
        "assets",
        "assets/images",
        "assets/sounds"
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✅ Created directory: {directory}")
        else:
            print(f"📁 Directory exists: {directory}")

def create_background_image():
    """Create a professional laboratory background image."""
    print("\n🎨 Creating Background Image...")
    
    try:
        # Create a 900x700 image
        width, height = 900, 700
        image = Image.new('RGB', (width, height), color='#0a0a0a')
        draw = ImageDraw.Draw(image)
        
        # Create gradient background
        for y in range(height):
            r = int(10 + (y / height) * 20)  # 10 to 30
            g = int(10 + (y / height) * 20)  # 10 to 30
            b = int(46 + (y / height) * 30)  # 46 to 76
            color = (r, g, b)
            draw.line([(0, y), (width, y)], fill=color)
        
        # Add laboratory equipment silhouettes
        equipment_color = '#333366'
        
        # Beakers
        for i, x in enumerate([80, 150, 220]):
            y = 500 + i * 10
            draw.ellipse([x, y, x+60, y+80], outline=equipment_color, width=2)
            draw.rectangle([x+15, y-40, x+45, y], outline=equipment_color, width=2)
        
        # Test tubes rack
        for i in range(5):
            x = 350 + i * 25
            draw.rectangle([x, 450, x+15, 600], outline=equipment_color, width=2)
            draw.ellipse([x, 445, x+15, 455], outline=equipment_color, width=2)
        
        # Microscope
        draw.ellipse([650, 480, 750, 580], outline=equipment_color, width=3)
        draw.rectangle([680, 380, 720, 480], outline=equipment_color, width=3)
        draw.rectangle([670, 580, 730, 620], outline=equipment_color, width=3)
        
        # Periodic table on wall
        draw.rectangle([50, 50, 250, 150], outline='#00d4ff', width=2)
        draw.text((60, 60), "PERIODIC TABLE", fill='#00d4ff')
        
        # Add molecular structures
        atoms = [(400, 200), (450, 180), (500, 200), (450, 240)]
        for i, (x, y) in enumerate(atoms):
            color = ['#39ff14', '#00d4ff', '#bf00ff', '#ff1493'][i]
            draw.ellipse([x-8, y-8, x+8, y+8], fill=color, outline=color)
        
        # Draw bonds between atoms
        bonds = [((400, 200), (450, 180)), ((450, 180), (500, 200)), 
                ((450, 180), (450, 240)), ((400, 200), (450, 240))]
        for (x1, y1), (x2, y2) in bonds:
            draw.line([(x1, y1), (x2, y2)], fill='#666666', width=2)
        
        # Add title overlay
        overlay = Image.new('RGBA', (width, height), (0, 0, 0, 0))
        overlay_draw = ImageDraw.Draw(overlay)
        overlay_draw.rectangle([0, 0, width, 120], fill=(0, 0, 0, 100))
        
        # Composite overlay
        image = Image.alpha_composite(image.convert('RGBA'), overlay).convert('RGB')
        draw = ImageDraw.Draw(image)
        
        # Add title
        try:
            font_large = ImageFont.truetype("arial.ttf", 32)
            font_small = ImageFont.truetype("arial.ttf", 16)
        except:
            font_large = ImageFont.load_default()
            font_small = ImageFont.load_default()
        
        title = "PERIODIC TABLE QUEST"
        subtitle = "Enhanced Laboratory Environment"
        
        # Center title
        bbox = draw.textbbox((0, 0), title, font=font_large)
        text_width = bbox[2] - bbox[0]
        text_x = (width - text_width) // 2
        
        draw.text((text_x, 25), title, fill='#00d4ff', font=font_large)
        
        # Center subtitle
        bbox2 = draw.textbbox((0, 0), subtitle, font=font_small)
        text_width2 = bbox2[2] - bbox2[0]
        text_x2 = (width - text_width2) // 2
        draw.text((text_x2, 70), subtitle, fill='#39ff14', font=font_small)
        
        # Save image
        image.save('assets/images/lab_bg.jpg', 'JPEG', quality=90)
        print("✅ Background image created: assets/images/lab_bg.jpg")
        return True
        
    except Exception as e:
        print(f"❌ Error creating background image: {e}")
        return False

def create_sound_effects():
    """Create all required sound effects."""
    print("\n🔊 Creating Sound Effects...")
    
    try:
        pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)
        
        # Create click sound
        print("   Creating click sound...")
        duration = 0.1
        sample_rate = 22050
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        frequency = 800
        wave = np.sin(frequency * 2 * np.pi * t) * 0.3
        
        # Apply envelope to avoid clicks
        envelope = np.exp(-t * 10)
        wave = wave * envelope
        
        sound_array = (wave * 32767).astype(np.int16)
        sound_array = np.repeat(sound_array.reshape(len(sound_array), 1), 2, axis=1)
        
        # Save as WAV
        import wave
        with wave.open('assets/sounds/click.wav', 'w') as wav_file:
            wav_file.setnchannels(2)
            wav_file.setsampwidth(2)
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(sound_array.tobytes())
        
        print("✅ Click sound created")
        
        # Create correct answer sound (ascending chord)
        print("   Creating correct answer sound...")
        duration = 0.8
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        
        # Create chord (C major)
        frequencies = [523.25, 659.25, 783.99]  # C5, E5, G5
        wave = np.zeros_like(t)
        for freq in frequencies:
            wave += np.sin(freq * 2 * np.pi * t) * 0.2
        
        # Apply envelope
        envelope = np.exp(-t * 2)
        wave = wave * envelope
        
        sound_array = (wave * 32767).astype(np.int16)
        sound_array = np.repeat(sound_array.reshape(len(sound_array), 1), 2, axis=1)
        
        with wave.open('assets/sounds/correct.wav', 'w') as wav_file:
            wav_file.setnchannels(2)
            wav_file.setsampwidth(2)
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(sound_array.tobytes())
        
        print("✅ Correct answer sound created")
        
        # Create incorrect answer sound (descending)
        print("   Creating incorrect answer sound...")
        frequencies = [400, 350, 300]  # Descending
        wave = np.zeros_like(t)
        for i, freq in enumerate(frequencies):
            start = i * len(t) // 3
            end = (i + 1) * len(t) // 3
            wave[start:end] = np.sin(freq * 2 * np.pi * t[start:end]) * 0.3
        
        envelope = np.exp(-t * 3)
        wave = wave * envelope
        
        sound_array = (wave * 32767).astype(np.int16)
        sound_array = np.repeat(sound_array.reshape(len(sound_array), 1), 2, axis=1)
        
        with wave.open('assets/sounds/incorrect.wav', 'w') as wav_file:
            wav_file.setnchannels(2)
            wav_file.setsampwidth(2)
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(sound_array.tobytes())
        
        print("✅ Incorrect answer sound created")
        
        # Create time up sound (alarm)
        print("   Creating time up sound...")
        duration = 1.5
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        
        # Create alarm sound (alternating frequencies)
        wave = np.zeros_like(t)
        for i in range(int(duration * 4)):  # 4 beeps per second
            start = i * len(t) // int(duration * 4)
            end = (i + 1) * len(t) // int(duration * 4)
            freq = 1000 if i % 2 == 0 else 800
            wave[start:end] = np.sin(freq * 2 * np.pi * t[start:end]) * 0.4
        
        sound_array = (wave * 32767).astype(np.int16)
        sound_array = np.repeat(sound_array.reshape(len(sound_array), 1), 2, axis=1)
        
        with wave.open('assets/sounds/time_up.wav', 'w') as wav_file:
            wav_file.setnchannels(2)
            wav_file.setsampwidth(2)
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(sound_array.tobytes())
        
        print("✅ Time up sound created")
        
        # Create background music (simple ambient)
        print("   Creating background music...")
        duration = 30  # 30 seconds, will loop
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        
        # Create ambient music with multiple sine waves
        wave = np.zeros_like(t)
        base_freq = 220  # A3
        
        # Add harmonics for ambient sound
        for harmonic in [1, 1.5, 2, 2.5, 3]:
            freq = base_freq * harmonic
            amplitude = 0.1 / harmonic  # Decreasing amplitude
            wave += np.sin(freq * 2 * np.pi * t) * amplitude
        
        # Add slow modulation
        modulation = np.sin(0.1 * 2 * np.pi * t) * 0.3 + 0.7
        wave = wave * modulation
        
        # Convert to MP3-like format (we'll save as WAV for simplicity)
        sound_array = (wave * 16383).astype(np.int16)  # Lower volume for background
        sound_array = np.repeat(sound_array.reshape(len(sound_array), 1), 2, axis=1)
        
        with wave.open('assets/sounds/background_music.wav', 'w') as wav_file:
            wav_file.setnchannels(2)
            wav_file.setsampwidth(2)
            wav_file.setframerate(sample_rate)
            wav_file.writeframes(sound_array.tobytes())
        
        # Also create an MP3 version (copy the WAV for now)
        import shutil
        shutil.copy('assets/sounds/background_music.wav', 'assets/sounds/background_music.mp3')
        
        print("✅ Background music created")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating sound effects: {e}")
        return False

def create_asset_info():
    """Create asset information file."""
    info = {
        "created": "Exhibition Asset Pack",
        "version": "1.0",
        "assets": {
            "background_image": {
                "file": "assets/images/lab_bg.jpg",
                "description": "Laboratory background with equipment and molecular structures",
                "size": "900x700"
            },
            "sounds": {
                "click": "assets/sounds/click.wav",
                "correct": "assets/sounds/correct.wav", 
                "incorrect": "assets/sounds/incorrect.wav",
                "time_up": "assets/sounds/time_up.wav",
                "background_music": "assets/sounds/background_music.mp3"
            }
        }
    }
    
    with open('assets/asset_info.json', 'w') as f:
        json.dump(info, f, indent=2)
    
    print("✅ Asset information file created")

def main():
    """Create all assets for the exhibition."""
    print("🧪 CREATING ALL EXHIBITION ASSETS")
    print("=" * 50)
    
    # Check dependencies
    try:
        import PIL
        print("✅ PIL/Pillow available")
    except ImportError:
        print("❌ PIL/Pillow required: pip install Pillow")
        return False
    
    try:
        import pygame
        print("✅ Pygame available")
    except ImportError:
        print("❌ Pygame required: pip install pygame")
        return False
    
    try:
        import numpy
        print("✅ NumPy available")
    except ImportError:
        print("❌ NumPy required: pip install numpy")
        return False
    
    print()
    
    # Create everything
    create_directories()
    
    bg_success = create_background_image()
    sound_success = create_sound_effects()
    
    create_asset_info()
    
    print("\n" + "=" * 50)
    print("📋 ASSET CREATION SUMMARY")
    print("=" * 50)
    
    if bg_success and sound_success:
        print("🎉 ALL ASSETS CREATED SUCCESSFULLY!")
        print("\n✅ Created:")
        print("   🎨 Professional laboratory background")
        print("   🔊 Complete sound effect library")
        print("   📄 Asset information file")
        print("\n🚀 Your exhibition is now fully enhanced!")
        return True
    else:
        print("⚠️ Some assets failed to create")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎯 Run 'python test_enhancements.py' to verify everything works!")
    else:
        print("\n💡 Install missing dependencies and try again")
