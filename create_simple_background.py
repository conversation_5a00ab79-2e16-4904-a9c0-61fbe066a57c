"""
Simple script to create a basic background image for the Periodic Table Quest game.
"""

import os
from PIL import Image, ImageDraw, ImageFont

def create_simple_background():
    """Create a simple laboratory background image."""
    try:
        # Create a 900x700 image with a gradient background
        width, height = 900, 700
        image = Image.new('RGB', (width, height), color='#1a1a2e')
        draw = ImageDraw.Draw(image)
        
        # Create a simple gradient effect
        for y in range(height):
            # Create a gradient from dark blue to dark purple
            r = int(26 + (y / height) * 30)  # 26 to 56
            g = int(26 + (y / height) * 20)  # 26 to 46
            b = int(46 + (y / height) * 40)  # 46 to 86
            color = (r, g, b)
            draw.line([(0, y), (width, y)], fill=color)
        
        # Add simple geometric shapes for lab equipment
        # Beaker outline
        draw.ellipse([50, 500, 150, 600], outline='#444466', width=2)
        draw.rectangle([70, 450, 130, 500], outline='#444466', width=2)
        
        # Test tubes
        for i in range(3):
            tube_x = 200 + i * 40
            draw.rectangle([tube_x, 400, tube_x + 20, 600], outline='#444466', width=2)
            draw.ellipse([tube_x, 395, tube_x + 20, 410], outline='#444466', width=2)
        
        # Microscope shape
        draw.ellipse([700, 450, 800, 550], outline='#444466', width=2)
        draw.rectangle([730, 350, 770, 450], outline='#444466', width=2)
        
        # Add title text overlay
        overlay = Image.new('RGBA', (width, height), (0, 0, 0, 0))
        overlay_draw = ImageDraw.Draw(overlay)
        overlay_draw.rectangle([0, 0, width, 150], fill=(0, 0, 0, 80))
        
        # Composite the overlay
        image = Image.alpha_composite(image.convert('RGBA'), overlay).convert('RGB')
        draw = ImageDraw.Draw(image)
        
        # Add title text
        try:
            font_large = ImageFont.truetype("arial.ttf", 36)
            font_small = ImageFont.truetype("arial.ttf", 18)
        except:
            font_large = ImageFont.load_default()
            font_small = ImageFont.load_default()
        
        title = "PERIODIC TABLE QUEST"
        subtitle = "Enhanced Laboratory Environment"
        
        # Calculate text position for centering
        bbox = draw.textbbox((0, 0), title, font=font_large)
        text_width = bbox[2] - bbox[0]
        text_x = (width - text_width) // 2
        
        draw.text((text_x, 40), title, fill='#00BFFF', font=font_large)
        
        bbox2 = draw.textbbox((0, 0), subtitle, font=font_small)
        text_width2 = bbox2[2] - bbox2[0]
        text_x2 = (width - text_width2) // 2
        draw.text((text_x2, 90), subtitle, fill='#39FF14', font=font_small)
        
        # Save the image
        os.makedirs('assets/images', exist_ok=True)
        image.save('assets/images/lab_bg.jpg', 'JPEG', quality=85)
        print("✅ Created sample background image: assets/images/lab_bg.jpg")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating background image: {e}")
        return False

if __name__ == "__main__":
    print("🎨 Creating simple background image...")
    success = create_simple_background()
    if success:
        print("🎉 Background image created successfully!")
        print("🚀 You can now run the enhanced game!")
    else:
        print("❌ Failed to create background image.")
