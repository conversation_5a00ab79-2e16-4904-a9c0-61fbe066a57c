<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Periodic Table Quest - Exhibition Demo</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #0a0a0a, #1a1a2e);
            font-family: Arial, sans-serif;
            color: white;
            overflow: hidden;
        }

        .container {
            width: 100vw;
            height: 100vh;
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .floating-atom {
            position: absolute;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .atom-1 { background: #00d4ff; top: 10%; left: 10%; animation-delay: 0s; }
        .atom-2 { background: #39ff14; top: 20%; left: 80%; animation-delay: 1s; }
        .atom-3 { background: #bf00ff; top: 70%; left: 15%; animation-delay: 2s; }
        .atom-4 { background: #ff1493; top: 80%; left: 85%; animation-delay: 3s; }
        .atom-5 { background: #ff6600; top: 40%; left: 5%; animation-delay: 4s; }
        .atom-6 { background: #ffd700; top: 60%; left: 90%; animation-delay: 5s; }

        @keyframes float {
            0%, 100% { transform: translateY(0px) translateX(0px); }
            25% { transform: translateY(-20px) translateX(10px); }
            50% { transform: translateY(0px) translateX(20px); }
            75% { transform: translateY(20px) translateX(10px); }
        }

        .main-panel {
            background: rgba(26, 26, 46, 0.9);
            border: 3px solid #00d4ff;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
            backdrop-filter: blur(10px);
            max-width: 600px;
        }

        .title {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 20px;
            animation: colorCycle 4s ease-in-out infinite;
        }

        @keyframes colorCycle {
            0% { color: #39ff14; }
            25% { color: #00d4ff; }
            50% { color: #bf00ff; }
            75% { color: #ff1493; }
            100% { color: #39ff14; }
        }

        .subtitle {
            color: #ffd700;
            font-size: 1.2em;
            margin-bottom: 30px;
            font-weight: bold;
        }

        .features {
            text-align: left;
            margin: 30px 0;
            line-height: 1.8;
        }

        .enhanced-button {
            display: block;
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            background: linear-gradient(45deg, #39ff14, #00d4ff);
            border: none;
            border-radius: 10px;
            color: white;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(57, 255, 20, 0.3);
        }

        .enhanced-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(57, 255, 20, 0.5);
            background: linear-gradient(45deg, #50ff30, #20e4ff);
        }

        .enhanced-button:active {
            transform: translateY(0px);
            background: linear-gradient(45deg, #ffd700, #ff6600);
        }

        .demo-text {
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #39ff14;
        }

        .status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(57, 255, 20, 0.2);
            padding: 15px;
            border-radius: 10px;
            border: 2px solid #39ff14;
        }

        .molecular-bond {
            position: absolute;
            height: 2px;
            background: linear-gradient(90deg, transparent, #00d4ff, transparent);
            animation: pulse 3s ease-in-out infinite;
        }

        .bond-1 { top: 30%; left: 20%; width: 100px; transform: rotate(45deg); }
        .bond-2 { top: 60%; left: 70%; width: 80px; transform: rotate(-30deg); }
        .bond-3 { top: 80%; left: 30%; width: 120px; transform: rotate(15deg); }

        @keyframes pulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Floating Atoms -->
        <div class="floating-atom atom-1"></div>
        <div class="floating-atom atom-2"></div>
        <div class="floating-atom atom-3"></div>
        <div class="floating-atom atom-4"></div>
        <div class="floating-atom atom-5"></div>
        <div class="floating-atom atom-6"></div>

        <!-- Molecular Bonds -->
        <div class="molecular-bond bond-1"></div>
        <div class="molecular-bond bond-2"></div>
        <div class="molecular-bond bond-3"></div>

        <!-- Main Panel -->
        <div class="main-panel">
            <h1 class="title">🧪 PERIODIC TABLE QUEST 🧪</h1>
            <div class="subtitle">✨ ENHANCED EXHIBITION VERSION ✨</div>
            
            <div class="demo-text">
                <strong>🎨 ENHANCED FEATURES ACTIVE:</strong><br>
                • Animated floating atoms background<br>
                • Glowing interactive buttons<br>
                • Color-cycling title animation<br>
                • Visual click effects<br>
                • Professional exhibition theme
            </div>

            <button class="enhanced-button" onclick="showDemo('play')">🎮 PLAY ENHANCED GAME</button>
            <button class="enhanced-button" onclick="showDemo('difficulty')">⚙️ DIFFICULTY SETTINGS</button>
            <button class="enhanced-button" onclick="showDemo('scores')">🏆 ANIMATED SCORES</button>
            <button class="enhanced-button" onclick="showDemo('help')">❓ INTERACTIVE HELP</button>
            
            <div class="demo-text" style="margin-top: 30px;">
                <strong>🚀 THIS IS WHAT YOUR PYTHON APP LOOKS LIKE!</strong><br>
                Your Periodic Table Quest now has all these visual enhancements working in the actual Python application.
            </div>
        </div>

        <!-- Status Indicator -->
        <div class="status">
            <strong>✅ EXHIBITION READY</strong><br>
            All enhancements active!
        </div>
    </div>

    <script>
        function showDemo(type) {
            const messages = {
                play: "🎮 ENHANCED GAME FEATURES:\n\n✨ Animated question interface\n🎯 Visual feedback for answers\n⏱️ Animated countdown timer\n🎨 Smooth transitions\n🔬 Laboratory theme throughout\n\nAll visual enhancements are active!",
                difficulty: "⚙️ DIFFICULTY ENHANCEMENTS:\n\n🟢 Easy: Animated green theme\n🔵 Medium: Animated blue theme\n🟣 Hard: Animated purple theme\n\nEach level has unique visual effects!",
                scores: "🏆 ENHANCED SCORING:\n\n🥇 Easy: 150 points\n🥈 Medium: 120 points\n🥉 Hard: 90 points\n\nScores animate with celebration effects!",
                help: "❓ INTERACTIVE HELP:\n\n📚 Animated tutorials\n🎯 Visual step-by-step guides\n✨ Interactive demonstrations\n🎨 Beautiful formatting\n\nHelp system with full visual enhancements!"
            };
            
            alert(messages[type]);
        }

        // Add some dynamic effects
        document.addEventListener('DOMContentLoaded', function() {
            // Create additional floating particles
            for (let i = 0; i < 10; i++) {
                setTimeout(() => {
                    const particle = document.createElement('div');
                    particle.className = 'floating-atom';
                    particle.style.background = ['#00d4ff', '#39ff14', '#bf00ff', '#ff1493', '#ff6600'][Math.floor(Math.random() * 5)];
                    particle.style.top = Math.random() * 100 + '%';
                    particle.style.left = Math.random() * 100 + '%';
                    particle.style.animationDelay = Math.random() * 6 + 's';
                    document.querySelector('.container').appendChild(particle);
                }, i * 500);
            }
        });
    </script>
</body>
</html>
