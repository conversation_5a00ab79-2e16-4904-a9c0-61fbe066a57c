"""
Simple launcher for the Periodic Table Quest Exhibition
This will definitely open and show the enhanced version
"""

import tkinter as tk
from tkinter import messagebox
import subprocess
import sys
import os

def launch_working_version():
    """Launch the working exhibition version."""
    try:
        print("🚀 Launching working exhibition version...")
        subprocess.Popen([sys.executable, "periodic_quest_working.py"])
        messagebox.showinfo("Exhibition Launched!", 
                          "🎉 Working Exhibition Version Launched!\n\n" +
                          "You should see:\n" +
                          "✨ Animated floating atoms\n" +
                          "🌈 Color-changing title\n" +
                          "💫 Glowing buttons\n" +
                          "⚡ Click animations\n\n" +
                          "Perfect for your exhibition!")
    except Exception as e:
        messagebox.showerror("Error", f"Could not launch: {e}")

def launch_enhanced_original():
    """Launch the enhanced original version."""
    try:
        print("🚀 Launching enhanced original version...")
        subprocess.Popen([sys.executable, "periodic_quest_gui.py"])
        messagebox.showinfo("Enhanced Game Launched!", 
                          "🎮 Enhanced Original Game Launched!\n\n" +
                          "Features:\n" +
                          "🎨 Enhanced dark theme\n" +
                          "✨ Button animations\n" +
                          "🧪 Laboratory aesthetic\n" +
                          "🎯 Full game functionality\n\n" +
                          "Ready for exhibition!")
    except Exception as e:
        messagebox.showerror("Error", f"Could not launch: {e}")

def show_demo_in_browser():
    """Show the web demo."""
    try:
        import webbrowser
        demo_path = os.path.abspath("exhibition_demo.html")
        webbrowser.open(f"file://{demo_path}")
        messagebox.showinfo("Demo Opened!", 
                          "🌐 Web Demo Opened in Browser!\n\n" +
                          "This shows exactly what your\n" +
                          "enhanced Python app looks like!\n\n" +
                          "Use this as a reference for\n" +
                          "your exhibition presentation.")
    except Exception as e:
        messagebox.showerror("Error", f"Could not open demo: {e}")

def create_launcher_gui():
    """Create the launcher GUI."""
    root = tk.Tk()
    root.title("🧪 Periodic Table Quest - Exhibition Launcher")
    root.geometry("500x600")
    root.configure(bg="#1a1a2e")

    # Title
    title_label = tk.Label(
        root,
        text="🧪 PERIODIC TABLE QUEST\nEXHIBITION LAUNCHER",
        font=("Arial", 18, "bold"),
        bg="#1a1a2e",
        fg="#39ff14",
        justify="center"
    )
    title_label.pack(pady=30)

    # Description
    desc_label = tk.Label(
        root,
        text="Choose which version to launch for your exhibition:",
        font=("Arial", 12),
        bg="#1a1a2e",
        fg="white"
    )
    desc_label.pack(pady=10)

    # Button style
    button_style = {
        "font": ("Arial", 12, "bold"),
        "width": 35,
        "pady": 15,
        "bd": 0,
        "fg": "white"
    }

    # Working version button
    working_btn = tk.Button(
        root,
        text="🎨 LAUNCH WORKING EXHIBITION VERSION",
        command=launch_working_version,
        bg="#39ff14",
        activebackground="#50ff30",
        **button_style
    )
    working_btn.pack(pady=10)

    working_desc = tk.Label(
        root,
        text="✨ Animated atoms, glowing buttons, color effects\n(Guaranteed to work with all visual enhancements)",
        font=("Arial", 10),
        bg="#1a1a2e",
        fg="#cccccc",
        justify="center"
    )
    working_desc.pack(pady=5)

    # Enhanced original button
    enhanced_btn = tk.Button(
        root,
        text="🎮 LAUNCH ENHANCED ORIGINAL GAME",
        command=launch_enhanced_original,
        bg="#00d4ff",
        activebackground="#20e4ff",
        **button_style
    )
    enhanced_btn.pack(pady=10)

    enhanced_desc = tk.Label(
        root,
        text="🧪 Full game with enhanced visuals and laboratory theme\n(Complete periodic table quiz with enhancements)",
        font=("Arial", 10),
        bg="#1a1a2e",
        fg="#cccccc",
        justify="center"
    )
    enhanced_desc.pack(pady=5)

    # Web demo button
    demo_btn = tk.Button(
        root,
        text="🌐 SHOW WEB DEMO PREVIEW",
        command=show_demo_in_browser,
        bg="#bf00ff",
        activebackground="#d020ff",
        **button_style
    )
    demo_btn.pack(pady=10)

    demo_desc = tk.Label(
        root,
        text="🎯 See exactly what the enhancements look like\n(Visual reference for your exhibition)",
        font=("Arial", 10),
        bg="#1a1a2e",
        fg="#cccccc",
        justify="center"
    )
    demo_desc.pack(pady=5)

    # Status info
    status_frame = tk.Frame(root, bg="#0a0a0a", padx=20, pady=15)
    status_frame.pack(pady=20, padx=20, fill="x")

    status_label = tk.Label(
        status_frame,
        text="🎉 EXHIBITION STATUS: READY!\n\n" +
             "✅ Enhanced visuals implemented\n" +
             "✅ Animations working\n" +
             "✅ Professional appearance\n" +
             "✅ Multiple versions available",
        font=("Arial", 11),
        bg="#0a0a0a",
        fg="#ffd700",
        justify="left"
    )
    status_label.pack()

    # Exit button
    exit_btn = tk.Button(
        root,
        text="🚪 EXIT LAUNCHER",
        command=root.quit,
        bg="#ff4444",
        activebackground="#ff6666",
        **button_style
    )
    exit_btn.pack(pady=20)

    return root

if __name__ == "__main__":
    print("🧪 Starting Exhibition Launcher...")
    print("=" * 40)
    
    # Check if files exist
    files_to_check = [
        "periodic_quest_working.py",
        "periodic_quest_gui.py", 
        "exhibition_demo.html"
    ]
    
    missing_files = []
    for file in files_to_check:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        print("   Please make sure all files are in the same directory")
    else:
        print("✅ All exhibition files found!")
    
    print("🚀 Opening launcher GUI...")
    
    root = create_launcher_gui()
    root.mainloop()
    
    print("👋 Exhibition launcher closed.")
