"""
Periodic Table Quest - A quiz game to test your knowledge of the periodic table.
"""

import random
import time
import json
import os
from periodic_data import ELEMENTS, DIFFICULTY_LEVELS, ELEMENT_GROUPS

class PeriodicTableQuest:
    """Main game class for Periodic Table Quest."""
    
    def __init__(self):
        """Initialize the game with default settings."""
        self.score = 0
        self.high_scores = self.load_high_scores()
        self.current_difficulty = "easy"
        self.question_time_limit = 30  # seconds
        self.hints_used = 0
        
    def load_high_scores(self):
        """Load high scores from file if it exists."""
        if os.path.exists("high_scores.json"):
            try:
                with open("high_scores.json", "r") as file:
                    return json.load(file)
            except:
                return {"easy": 0, "medium": 0, "hard": 0}
        else:
            return {"easy": 0, "medium": 0, "hard": 0}
    
    def save_high_scores(self):
        """Save high scores to file."""
        with open("high_scores.json", "w") as file:
            json.dump(self.high_scores, file)
    
    def update_high_score(self):
        """Update high score if current score is higher."""
        if self.score > self.high_scores[self.current_difficulty]:
            self.high_scores[self.current_difficulty] = self.score
            self.save_high_scores()
            return True
        return False
    
    def set_difficulty(self, difficulty):
        """Set the game difficulty level."""
        if difficulty in DIFFICULTY_LEVELS:
            self.current_difficulty = difficulty
            # Adjust time limit based on difficulty
            if difficulty == "easy":
                self.question_time_limit = 30
            elif difficulty == "medium":
                self.question_time_limit = 25
            else:  # hard
                self.question_time_limit = 20
            return True
        return False
    
    def generate_question(self):
        """Generate a random question based on current difficulty."""
        # Get elements available for the current difficulty
        available_elements = DIFFICULTY_LEVELS[self.current_difficulty]
        
        # Choose a random element
        element_number = random.choice(available_elements)
        element = ELEMENTS[element_number]
        
        # Choose a question type
        question_types = [
            "symbol_to_name",  # What element has the symbol X?
            "name_to_symbol",  # What is the symbol for X?
            "number_to_element",  # What element has atomic number X?
            "element_to_number",  # What is the atomic number of X?
            "group_to_element",  # Name an element in the X group
        ]
        
        question_type = random.choice(question_types)
        
        if question_type == "symbol_to_name":
            question = f"What element has the symbol '{element['symbol']}'?"
            answer = element['name']
            hint = f"This element has atomic number {element_number}."
            
        elif question_type == "name_to_symbol":
            question = f"What is the symbol for {element['name']}?"
            answer = element['symbol']
            hint = f"This element has atomic number {element_number}."
            
        elif question_type == "number_to_element":
            question = f"What element has atomic number {element_number}?"
            answer = element['name']
            hint = f"The symbol for this element is '{element['symbol']}'."
            
        elif question_type == "element_to_number":
            question = f"What is the atomic number of {element['name']}?"
            answer = str(element_number)
            hint = f"The symbol for this element is '{element['symbol']}'."
            
        elif question_type == "group_to_element":
            group = element['group']
            question = f"Name an element in the {group} group."
            # Any element in this group is a valid answer
            group_elements = [ELEMENTS[num]['name'] for num in ELEMENT_GROUPS.get(group, [])]
            answer = group_elements  # List of valid answers
            hint = f"There are {len(group_elements)} elements in this group."
        
        return {
            "question": question,
            "answer": answer,
            "hint": hint,
            "fact": element['fact'],
            "element_number": element_number
        }
    
    def check_answer(self, user_answer, correct_answer):
        """Check if the user's answer is correct."""
        if isinstance(correct_answer, list):
            # For group questions, check if answer is in the list of valid answers
            return user_answer.lower() in [a.lower() for a in correct_answer]
        else:
            # For other questions, do a direct comparison
            return user_answer.lower() == correct_answer.lower()
    
    def ask_question(self):
        """Ask a question and handle the user's answer."""
        question_data = self.generate_question()
        print("\n" + "="*50)
        print(f"Question: {question_data['question']}")
        
        # Start timer
        start_time = time.time()
        hint_used = False
        
        while True:
            # Check if time is up
            elapsed_time = time.time() - start_time
            remaining_time = self.question_time_limit - elapsed_time
            
            if remaining_time <= 0:
                print("\nTime's up! The correct answer was:", 
                      question_data['answer'] if not isinstance(question_data['answer'], list) 
                      else " or ".join(question_data['answer']))
                return False
            
            # Show remaining time
            print(f"\nTime remaining: {int(remaining_time)} seconds")
            
            # Get user input with option for hint
            user_input = input("Your answer (or type 'hint' for a hint): ").strip()
            
            if user_input.lower() == "hint":
                if not hint_used:
                    print(f"Hint: {question_data['hint']}")
                    hint_used = True
                    self.hints_used += 1
                else:
                    print("You've already used a hint for this question!")
                continue
            
            # Check answer
            if self.check_answer(user_input, question_data['answer']):
                # Calculate points based on time taken and if hint was used
                points = max(1, int(remaining_time / 5))
                if hint_used:
                    points = max(1, points // 2)  # Half points if hint was used
                
                self.score += points
                print(f"\nCorrect! You earned {points} points.")
                print(f"Fun Fact: {question_data['fact']}")
                return True
            else:
                print("\nIncorrect! The correct answer was:", 
                      question_data['answer'] if not isinstance(question_data['answer'], list) 
                      else " or ".join(question_data['answer']))
                return False
    
    def play_round(self, num_questions=5):
        """Play a round of the game with a specified number of questions."""
        self.score = 0
        self.hints_used = 0
        
        print(f"\nStarting a new round on {self.current_difficulty.upper()} difficulty!")
        print(f"You'll have {self.question_time_limit} seconds to answer each question.")
        
        correct_answers = 0
        for i in range(num_questions):
            print(f"\nQuestion {i+1} of {num_questions}")
            if self.ask_question():
                correct_answers += 1
        
        # Round summary
        print("\n" + "="*50)
        print(f"Round Complete! You got {correct_answers} out of {num_questions} correct.")
        print(f"Final Score: {self.score} points")
        print(f"Hints Used: {self.hints_used}")
        
        # Check for high score
        if self.update_high_score():
            print(f"New high score for {self.current_difficulty} difficulty!")
        
        print(f"Current High Score ({self.current_difficulty}): {self.high_scores[self.current_difficulty]}")
        
        return self.score
    
    def display_menu(self):
        """Display the main menu and handle user input."""
        while True:
            print("\n" + "="*50)
            print("🧪 PERIODIC TABLE QUEST 🧪")
            print("="*50)
            print("1. Play Game")
            print("2. Change Difficulty (Current: " + self.current_difficulty.upper() + ")")
            print("3. View High Scores")
            print("4. How to Play")
            print("5. Exit")
            
            choice = input("\nEnter your choice (1-5): ").strip()
            
            if choice == "1":
                self.play_round()
            elif choice == "2":
                self.change_difficulty_menu()
            elif choice == "3":
                self.display_high_scores()
            elif choice == "4":
                self.display_instructions()
            elif choice == "5":
                print("\nThanks for playing Periodic Table Quest! Goodbye!")
                break
            else:
                print("\nInvalid choice. Please try again.")
    
    def change_difficulty_menu(self):
        """Display the difficulty selection menu."""
        print("\n" + "="*50)
        print("DIFFICULTY SELECTION")
        print("="*50)
        print("1. Easy (Elements 1-20)")
        print("2. Medium (Elements 1-40)")
        print("3. Hard (Elements 1-60)")
        
        choice = input("\nSelect difficulty (1-3): ").strip()
        
        if choice == "1":
            self.set_difficulty("easy")
            print("Difficulty set to EASY.")
        elif choice == "2":
            self.set_difficulty("medium")
            print("Difficulty set to MEDIUM.")
        elif choice == "3":
            self.set_difficulty("hard")
            print("Difficulty set to HARD.")
        else:
            print("Invalid choice. Keeping current difficulty.")
    
    def display_high_scores(self):
        """Display the high scores for all difficulty levels."""
        print("\n" + "="*50)
        print("HIGH SCORES")
        print("="*50)
        print(f"Easy: {self.high_scores['easy']} points")
        print(f"Medium: {self.high_scores['medium']} points")
        print(f"Hard: {self.high_scores['hard']} points")
    
    def display_instructions(self):
        """Display game instructions."""
        print("\n" + "="*50)
        print("HOW TO PLAY")
        print("="*50)
        print("1. Select a difficulty level (Easy, Medium, Hard)")
        print("2. Answer questions about the periodic table")
        print("3. Type 'hint' to get a hint (reduces points)")
        print("4. Answer quickly for more points!")
        print("5. Try to beat your high score")
        print("\nQuestion Types:")
        print("- Element symbols and names")
        print("- Atomic numbers")
        print("- Element groups")
        print("\nGood luck!")

if __name__ == "__main__":
    game = PeriodicTableQuest()
    game.display_menu()
