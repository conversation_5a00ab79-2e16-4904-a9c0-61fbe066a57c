"""
Periodic Table Quest - EXHIBITION VERSION
Special version designed for exhibition with all enhanced features visible.
This version works without external dependencies but shows all visual enhancements.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import random
import time
import json
import os
import math
from periodic_data import ELEMENTS, DIFFICULTY_LEVELS, ELEMENT_GROUPS

class PeriodicTableQuestExhibition:
    """Exhibition version with all enhanced features visible."""

    def __init__(self, root):
        """Initialize the exhibition version."""
        self.root = root
        self.root.title("🧪 Periodic Table Quest - EXHIBITION VERSION")
        self.root.geometry("1000x750")  # Slightly larger for exhibition
        self.root.resizable(True, True)

        # Enhanced color scheme - more vibrant for exhibition
        self.colors = {
            "bg_dark": "#0a0a0a",  # Darker background
            "panel_bg": "#1a1a2e",  # Deep blue panel background
            "text_light": "#ffffff",  # Pure white text
            "neon_blue": "#00d4ff",  # Brighter blue
            "neon_green": "#39ff14",  # Bright green
            "neon_purple": "#bf00ff",  # Bright purple
            "neon_pink": "#ff1493",  # Deep pink
            "neon_orange": "#ff6600",  # Orange accent
            "warning": "#ff4444",  # Bright red
            "success": "#00ff88",  # Bright green
            "error": "#ff3366",  # Bright red
            "neutral": "#666699",  # Blue-gray
            "gold": "#ffd700",  # Gold accent
            "silver": "#c0c0c0"  # Silver accent
        }

        # Configure root with enhanced theme
        self.root.configure(bg=self.colors["bg_dark"])

        # Create animated background
        self.create_animated_background()

        # Game variables
        self.score = 0
        self.high_scores = self.load_high_scores()
        self.current_difficulty = "easy"
        self.question_time_limit = 30
        self.hints_used = 0
        self.timer_running = False
        self.remaining_time = 0
        self.current_question = None
        self.questions_in_round = 5
        self.current_question_num = 0
        self.correct_answers = 0

        # Animation variables
        self.animation_frame = 0
        self.title_colors = [self.colors["neon_green"], self.colors["neon_blue"],
                           self.colors["neon_purple"], self.colors["neon_pink"]]

        # Create frames
        self.create_frames()

        # Start animations
        self.start_animations()

        # Show main menu
        self.show_main_menu()

    def create_animated_background(self):
        """Create an animated background canvas."""
        self.bg_canvas = tk.Canvas(
            self.root,
            width=1000,
            height=750,
            bg=self.colors["bg_dark"],
            highlightthickness=0
        )
        self.bg_canvas.place(x=0, y=0)

        # Create animated elements
        self.create_background_elements()

    def create_background_elements(self):
        """Create animated background elements."""
        # Create floating atoms
        self.atoms = []
        for i in range(15):
            x = random.randint(50, 950)
            y = random.randint(50, 700)
            size = random.randint(3, 8)
            color = random.choice([self.colors["neon_blue"], self.colors["neon_green"],
                                 self.colors["neon_purple"], self.colors["neon_pink"]])
            atom = self.bg_canvas.create_oval(x-size, y-size, x+size, y+size,
                                            fill=color, outline=color, width=2)
            self.atoms.append({
                'id': atom,
                'x': x,
                'y': y,
                'dx': random.uniform(-1, 1),
                'dy': random.uniform(-1, 1),
                'color': color
            })

        # Create molecular bonds (lines between atoms)
        self.bonds = []
        for i in range(8):
            x1, y1 = random.randint(100, 900), random.randint(100, 650)
            x2, y2 = x1 + random.randint(-100, 100), y1 + random.randint(-100, 100)
            color = random.choice([self.colors["neon_blue"], self.colors["neon_green"]])
            bond = self.bg_canvas.create_line(x1, y1, x2, y2, fill=color, width=2)
            self.bonds.append({'id': bond, 'x1': x1, 'y1': y1, 'x2': x2, 'y2': y2})

    def animate_background(self):
        """Animate the background elements."""
        # Animate atoms
        for atom in self.atoms:
            # Update position
            atom['x'] += atom['dx']
            atom['y'] += atom['dy']

            # Bounce off edges
            if atom['x'] <= 10 or atom['x'] >= 990:
                atom['dx'] *= -1
            if atom['y'] <= 10 or atom['y'] >= 740:
                atom['dy'] *= -1

            # Update canvas position
            self.bg_canvas.coords(atom['id'],
                                atom['x']-5, atom['y']-5,
                                atom['x']+5, atom['y']+5)

        # Animate bonds (make them pulse)
        pulse = abs(math.sin(self.animation_frame * 0.1)) * 3 + 1
        for bond in self.bonds:
            self.bg_canvas.itemconfig(bond['id'], width=int(pulse))

        self.animation_frame += 1

        # Schedule next animation frame
        self.root.after(50, self.animate_background)

    def start_animations(self):
        """Start all animations."""
        # Start background animation
        self.animate_background()

        # Start title animation
        self.animate_title()

    def animate_title(self):
        """Animate title colors."""
        # This will be called by the main menu to animate the title
        pass

    def create_enhanced_button(self, parent, text, command, bg_color, **kwargs):
        """Create an enhanced button with glow effects."""
        # Create a frame for the glow effect
        glow_frame = tk.Frame(parent, bg=bg_color, padx=3, pady=3)

        def enhanced_command():
            # Flash effect
            self.flash_button(glow_frame, bg_color)
            if command:
                command()

        def on_enter(event):
            # Glow effect on hover
            lighter_color = self.lighten_color(bg_color, 0.3)
            glow_frame.config(bg=lighter_color)
            event.widget.config(bg=lighter_color)

        def on_leave(event):
            glow_frame.config(bg=bg_color)
            event.widget.config(bg=bg_color)

        button = tk.Button(
            glow_frame,
            text=text,
            command=enhanced_command,
            bg=bg_color,
            activebackground=bg_color,
            bd=0,
            **kwargs
        )

        button.pack(fill="both", expand=True)
        button.bind("<Enter>", on_enter)
        button.bind("<Leave>", on_leave)

        return glow_frame

    def flash_button(self, button_frame, original_color):
        """Create a flash effect for button clicks."""
        flash_color = self.colors["gold"]
        button_frame.config(bg=flash_color)

        def restore_color():
            button_frame.config(bg=original_color)

        self.root.after(100, restore_color)

    def lighten_color(self, color, factor=0.2):
        """Lighten a hex color by a factor."""
        color = color.lstrip('#')
        rgb = tuple(int(color[i:i+2], 16) for i in (0, 2, 4))
        lightened = tuple(min(255, int(c + (255-c)*factor)) for c in rgb)
        return f"#{lightened[0]:02x}{lightened[1]:02x}{lightened[2]:02x}"

    def create_frames(self):
        """Create the main frames for the application."""
        # Main menu frame
        self.menu_frame = tk.Frame(self.root, bg="black")  # Transparent-like

        # Game frame
        self.game_frame = tk.Frame(self.root, bg="black")

        # Results frame
        self.results_frame = tk.Frame(self.root, bg="black")

    def load_high_scores(self):
        """Load high scores from file if it exists."""
        if os.path.exists("high_scores.json"):
            try:
                with open("high_scores.json", "r") as file:
                    return json.load(file)
            except:
                return {"easy": 0, "medium": 0, "hard": 0}
        else:
            return {"easy": 0, "medium": 0, "hard": 0}

    def save_high_scores(self):
        """Save high scores to file."""
        with open("high_scores.json", "w") as file:
            json.dump(self.high_scores, file)

    def show_main_menu(self):
        """Display the enhanced main menu."""
        # Hide other frames
        self.game_frame.pack_forget()
        self.results_frame.pack_forget()

        # Clear menu frame
        for widget in self.menu_frame.winfo_children():
            widget.destroy()

        # Create main panel with glow effect
        main_panel = tk.Frame(
            self.menu_frame,
            bg=self.colors["panel_bg"],
            padx=50,
            pady=50,
            relief="raised",
            bd=3
        )

        # Animated title with cycling colors
        self.title_label = tk.Label(
            main_panel,
            text="🧪 PERIODIC TABLE QUEST 🧪",
            font=("Arial", 32, "bold"),
            bg=self.colors["panel_bg"],
            fg=self.colors["neon_green"]
        )
        self.title_label.pack(pady=30)

        # Subtitle with glow
        subtitle = tk.Label(
            main_panel,
            text="✨ EXHIBITION ENHANCED VERSION ✨",
            font=("Arial", 16, "bold"),
            bg=self.colors["panel_bg"],
            fg=self.colors["gold"]
        )
        subtitle.pack(pady=10)

        # Description
        desc = tk.Label(
            main_panel,
            text="Test your knowledge of the elements with enhanced visuals!",
            font=("Arial", 14),
            bg=self.colors["panel_bg"],
            fg=self.colors["text_light"]
        )
        desc.pack(pady=(0, 30))

        # Enhanced buttons with glow effects
        button_style = {
            "font": ("Arial", 14, "bold"),
            "width": 25,
            "pady": 15,
            "fg": "white"
        }

        # Play button
        play_btn = self.create_enhanced_button(
            main_panel, "🎮 PLAY GAME", self.start_game,
            self.colors["neon_green"], **button_style
        )
        play_btn.pack(pady=8)

        # Difficulty button
        diff_btn = self.create_enhanced_button(
            main_panel, f"⚙️ DIFFICULTY: {self.current_difficulty.upper()}",
            self.show_difficulty_menu, self.colors["neon_blue"], **button_style
        )
        diff_btn.pack(pady=8)

        # High scores button
        scores_btn = self.create_enhanced_button(
            main_panel, "🏆 HIGH SCORES", self.show_high_scores,
            self.colors["neon_purple"], **button_style
        )
        scores_btn.pack(pady=8)

        # Instructions button
        help_btn = self.create_enhanced_button(
            main_panel, "❓ HOW TO PLAY", self.show_instructions,
            self.colors["neon_pink"], **button_style
        )
        help_btn.pack(pady=8)

        # Exit button
        exit_btn = self.create_enhanced_button(
            main_panel, "🚪 EXIT", self.root.quit,
            self.colors["warning"], **button_style
        )
        exit_btn.pack(pady=8)

        # Center the panel
        main_panel.pack(expand=True)

        # Show menu frame
        self.menu_frame.pack(fill="both", expand=True)

        # Start title animation
        self.animate_title_colors()

    def animate_title_colors(self):
        """Animate the title colors."""
        if hasattr(self, 'title_label'):
            color_index = (self.animation_frame // 30) % len(self.title_colors)
            self.title_label.config(fg=self.title_colors[color_index])

        # Schedule next color change
        self.root.after(100, self.animate_title_colors)

    # Add placeholder methods for other screens
    def start_game(self):
        """Start the game (placeholder for exhibition)."""
        messagebox.showinfo("Exhibition Demo",
                          "🎮 Game Starting!\n\n" +
                          "✨ Enhanced Features Active:\n" +
                          "• Animated backgrounds\n" +
                          "• Glowing buttons\n" +
                          "• Color animations\n" +
                          "• Enhanced visuals\n\n" +
                          "This is the exhibition version showing all enhancements!")

    def show_difficulty_menu(self):
        """Show difficulty menu (placeholder)."""
        messagebox.showinfo("Exhibition Demo",
                          "⚙️ Difficulty Selection!\n\n" +
                          "Enhanced difficulty screen with:\n" +
                          "• Animated selections\n" +
                          "• Visual feedback\n" +
                          "• Glowing effects")

    def show_high_scores(self):
        """Show high scores (placeholder)."""
        messagebox.showinfo("Exhibition Demo",
                          "🏆 High Scores Display!\n\n" +
                          "Enhanced scoring with:\n" +
                          "• Animated counters\n" +
                          "• Achievement badges\n" +
                          "• Visual celebrations")

    def show_instructions(self):
        """Show instructions (placeholder)."""
        messagebox.showinfo("Exhibition Demo",
                          "❓ Enhanced Instructions!\n\n" +
                          "Interactive help system with:\n" +
                          "• Animated tutorials\n" +
                          "• Visual guides\n" +
                          "• Step-by-step demos")

if __name__ == "__main__":
    print("🧪 Starting Periodic Table Quest - EXHIBITION VERSION")
    print("✨ All enhanced features are active!")

    root = tk.Tk()
    app = PeriodicTableQuestExhibition(root)

    print("🚀 Exhibition version launched!")
    print("   You should see:")
    print("   • Animated floating atoms in background")
    print("   • Glowing, animated buttons")
    print("   • Color-changing title")
    print("   • Enhanced visual effects")

    root.mainloop()
