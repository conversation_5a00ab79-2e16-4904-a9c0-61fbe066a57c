"""
Periodic Table Quest - WEB DEMO STYLE VERSION
This version recreates the exact visual style of the web demo in Python.
Features floating atoms, gradient backgrounds, and identical animations.
"""

import tkinter as tk
from tkinter import messagebox
import random
import json
import os
import time
import threading
import math

# Safe imports with proper error handling
PIL_AVAILABLE = False
PYGAME_AVAILABLE = False

try:
    from PIL import Image, ImageTk
    PIL_AVAILABLE = True
    print("✅ PIL/Pillow: Available - Background images enabled")
except ImportError:
    print("ℹ️ PIL/Pillow: Not available - Using enhanced colors instead")

try:
    import pygame
    pygame.mixer.init()
    PYGAME_AVAILABLE = True
    print("✅ Pygame: Available - Sound effects enabled")
except ImportError:
    print("ℹ️ Pygame: Not available - Using visual feedback instead")
except Exception:
    print("ℹ️ Pygame: Audio system not available - Using visual feedback instead")

# Import periodic data
try:
    from periodic_data import ELEMENTS, DIFFICULTY_LEVELS, ELEMENT_GROUPS
    print("✅ Periodic data loaded successfully")
except ImportError:
    # Fallback data if periodic_data.py is missing
    ELEMENTS = {
        1: {"symbol": "H", "name": "Hydrogen", "group": "Nonmetal"},
        2: {"symbol": "He", "name": "Helium", "group": "Noble Gas"},
        6: {"symbol": "C", "name": "Carbon", "group": "Nonmetal"},
        8: {"symbol": "O", "name": "Oxygen", "group": "Nonmetal"},
    }
    DIFFICULTY_LEVELS = {
        "easy": {"range": (1, 10), "time_limit": 30},
        "medium": {"range": (1, 20), "time_limit": 25},
        "hard": {"range": (1, 30), "time_limit": 20}
    }
    ELEMENT_GROUPS = ["Nonmetal", "Noble Gas", "Metal"]
    print("ℹ️ Using fallback periodic data")

class PeriodicTableQuestFixed:
    """Web Demo Style version - recreates the exact web demo appearance."""

    def __init__(self, root):
        """Initialize the web demo style version."""
        self.root = root
        self.root.title("🧪 Periodic Table Quest - Web Demo Style")
        self.root.geometry("1200x800")  # Match web demo size
        self.root.configure(bg="#0a0a0a")

        # Exact colors from web demo
        self.colors = {
            "bg_dark": "#0a0a0a",
            "bg_gradient": "#1a1a2e",
            "panel_bg": "#1a1a2e",
            "panel_border": "#00d4ff",
            "text_light": "#ffffff",
            "neon_blue": "#00d4ff",
            "neon_green": "#39ff14",
            "neon_purple": "#bf00ff",
            "neon_pink": "#ff1493",
            "neon_orange": "#ff6600",
            "gold": "#ffd700",
            "silver": "#c0c0c0",
            "success": "#00ff88",
            "error": "#ff3366",
            "warning": "#ff6600"
        }

        # Animation variables
        self.animation_frame = 0
        self.title_colors = [self.colors["neon_green"], self.colors["neon_blue"],
                           self.colors["neon_purple"], self.colors["neon_pink"]]
        self.current_title_color = 0

        # Game variables
        self.score = 0
        self.high_scores = self.load_high_scores()
        self.current_difficulty = "easy"
        self.current_question = None
        self.questions_answered = 0
        self.correct_answers = 0

        # Create animated background canvas
        self.create_animated_background()

        # Create web demo style interface
        self.create_web_demo_interface()

        # Start animations
        self.start_animations()

        print("🎉 Web Demo Style Periodic Table Quest initialized successfully!")

    def load_high_scores(self):
        """Load high scores safely."""
        try:
            if os.path.exists("high_scores.json"):
                with open("high_scores.json", "r") as f:
                    return json.load(f)
        except:
            pass
        return {"easy": 0, "medium": 0, "hard": 0}

    def save_high_scores(self):
        """Save high scores safely."""
        try:
            with open("high_scores.json", "w") as f:
                json.dump(self.high_scores, f)
        except:
            pass

    def create_animated_background(self):
        """Create animated background exactly like the web demo."""
        # Create full-screen canvas for background
        self.bg_canvas = tk.Canvas(
            self.root,
            width=1200,
            height=800,
            bg=self.colors["bg_dark"],
            highlightthickness=0
        )
        self.bg_canvas.place(x=0, y=0)

        # Create gradient background effect
        self.create_gradient_background()

        # Create floating atoms (exactly like web demo)
        self.create_floating_atoms()

        # Create molecular bonds
        self.create_molecular_bonds()

    def create_gradient_background(self):
        """Create gradient background effect like CSS."""
        # Create gradient rectangles to simulate CSS linear-gradient
        for y in range(0, 800, 10):
            # Calculate gradient color from #0a0a0a to #1a1a2e
            ratio = y / 800
            r = int(10 + ratio * 16)  # 0a to 1a
            g = int(10 + ratio * 16)  # 0a to 1a
            b = int(10 + ratio * 36)  # 0a to 2e
            color = f"#{r:02x}{g:02x}{b:02x}"

            self.bg_canvas.create_rectangle(
                0, y, 1200, y + 10,
                fill=color, outline=color
            )

    def create_floating_atoms(self):
        """Create floating atoms exactly like the web demo."""
        self.atoms = []

        # Exact positions and colors from web demo
        atom_data = [
            {"color": "#00d4ff", "x": 120, "y": 80, "delay": 0},    # atom-1
            {"color": "#39ff14", "x": 960, "y": 160, "delay": 1},   # atom-2
            {"color": "#bf00ff", "x": 180, "y": 560, "delay": 2},   # atom-3
            {"color": "#ff1493", "x": 1020, "y": 640, "delay": 3},  # atom-4
            {"color": "#ff6600", "x": 60, "y": 320, "delay": 4},    # atom-5
            {"color": "#ffd700", "x": 1080, "y": 480, "delay": 5}   # atom-6
        ]

        for i, data in enumerate(atom_data):
            # Create outer glow
            outer = self.bg_canvas.create_oval(
                data["x"] - 8, data["y"] - 8,
                data["x"] + 8, data["y"] + 8,
                fill="", outline=data["color"], width=2
            )

            # Create inner atom
            inner = self.bg_canvas.create_oval(
                data["x"] - 5, data["y"] - 5,
                data["x"] + 5, data["y"] + 5,
                fill=data["color"], outline=data["color"]
            )

            self.atoms.append({
                "outer": outer,
                "inner": inner,
                "x": data["x"],
                "y": data["y"],
                "base_x": data["x"],
                "base_y": data["y"],
                "delay": data["delay"],
                "color": data["color"]
            })

    def create_molecular_bonds(self):
        """Create molecular bonds exactly like web demo."""
        self.bonds = []

        # Bond positions from web demo
        bond_data = [
            {"x": 240, "y": 240, "width": 100, "angle": 45},   # bond-1
            {"x": 840, "y": 480, "width": 80, "angle": -30},   # bond-2
            {"x": 360, "y": 640, "width": 120, "angle": 15}    # bond-3
        ]

        for data in bond_data:
            # Calculate end points based on angle
            angle_rad = math.radians(data["angle"])
            x1 = data["x"]
            y1 = data["y"]
            x2 = x1 + data["width"] * math.cos(angle_rad)
            y2 = y1 + data["width"] * math.sin(angle_rad)

            bond = self.bg_canvas.create_line(
                x1, y1, x2, y2,
                fill=self.colors["neon_blue"],
                width=2
            )

            self.bonds.append({
                "id": bond,
                "x1": x1, "y1": y1, "x2": x2, "y2": y2
            })

    def create_web_demo_interface(self):
        """Create the main interface exactly like the web demo."""
        # Main panel (centered, exactly like web demo)
        panel_x = 300
        panel_y = 150
        panel_width = 600
        panel_height = 500

        # Create panel background with border (like web demo)
        self.panel_bg = self.bg_canvas.create_rectangle(
            panel_x, panel_y, panel_x + panel_width, panel_y + panel_height,
            fill=self.colors["panel_bg"],
            outline=self.colors["panel_border"],
            width=3
        )

        # Create glow effect around panel
        for i in range(1, 6):
            self.bg_canvas.create_rectangle(
                panel_x - i*2, panel_y - i*2,
                panel_x + panel_width + i*2, panel_y + panel_height + i*2,
                fill="", outline=self.colors["panel_border"],
                width=1
            )

        # Title (animated, exactly like web demo)
        self.title_text = self.bg_canvas.create_text(
            600, 220,  # Center of panel
            text="🧪 PERIODIC TABLE QUEST 🧪",
            font=("Arial", 24, "bold"),
            fill=self.colors["neon_green"],
            anchor="center"
        )

        # Subtitle
        self.subtitle_text = self.bg_canvas.create_text(
            600, 260,
            text="✨ ENHANCED EXHIBITION VERSION ✨",
            font=("Arial", 14, "bold"),
            fill=self.colors["gold"],
            anchor="center"
        )

        # Features text box (like web demo)
        features_text = """🎨 ENHANCED FEATURES ACTIVE:
• Animated floating atoms background
• Glowing interactive buttons
• Color-cycling title animation
• Visual click effects
• Professional exhibition theme"""

        # Create features background
        self.bg_canvas.create_rectangle(
            320, 290, 880, 390,
            fill="#000000", outline=self.colors["neon_green"], width=2
        )

        self.features_text = self.bg_canvas.create_text(
            600, 340,
            text=features_text,
            font=("Arial", 11),
            fill=self.colors["text_light"],
            anchor="center",
            justify="center"
        )

        # Create enhanced buttons exactly like web demo
        self.create_web_demo_buttons()

        # Status indicator (top right, like web demo)
        self.create_status_indicator()

    def create_web_demo_buttons(self):
        """Create buttons exactly like the web demo."""
        self.buttons = []

        button_data = [
            {"text": "🎮 PLAY ENHANCED GAME", "y": 420, "color": self.colors["neon_green"]},
            {"text": "⚙️ DIFFICULTY SETTINGS", "y": 470, "color": self.colors["neon_blue"]},
            {"text": "🏆 ANIMATED SCORES", "y": 520, "color": self.colors["neon_purple"]},
            {"text": "❓ INTERACTIVE HELP", "y": 570, "color": self.colors["neon_pink"]}
        ]

        for data in button_data:
            button = self.create_enhanced_web_button(
                600, data["y"], data["text"], data["color"]
            )
            self.buttons.append(button)

    def create_enhanced_web_button(self, x, y, text, color):
        """Create a button exactly like the web demo."""
        button_width = 400
        button_height = 35

        # Create button background with gradient effect
        button_bg = self.bg_canvas.create_rectangle(
            x - button_width//2, y - button_height//2,
            x + button_width//2, y + button_height//2,
            fill=color, outline=color, width=2
        )

        # Create button text
        button_text = self.bg_canvas.create_text(
            x, y, text=text,
            font=("Arial", 12, "bold"),
            fill="white", anchor="center"
        )

        # Create glow effect
        glow_effects = []
        for i in range(1, 4):
            glow = self.bg_canvas.create_rectangle(
                x - button_width//2 - i, y - button_height//2 - i,
                x + button_width//2 + i, y + button_height//2 + i,
                fill="", outline=color, width=1
            )
            glow_effects.append(glow)

        button_obj = {
            "bg": button_bg,
            "text": button_text,
            "glow": glow_effects,
            "x": x, "y": y,
            "width": button_width,
            "height": button_height,
            "color": color,
            "text_content": text
        }

        # Bind click events
        self.bg_canvas.tag_bind(button_bg, "<Button-1>",
                               lambda e: self.web_button_click(button_obj))
        self.bg_canvas.tag_bind(button_text, "<Button-1>",
                               lambda e: self.web_button_click(button_obj))

        # Bind hover events
        self.bg_canvas.tag_bind(button_bg, "<Enter>",
                               lambda e: self.web_button_hover_enter(button_obj))
        self.bg_canvas.tag_bind(button_bg, "<Leave>",
                               lambda e: self.web_button_hover_leave(button_obj))
        self.bg_canvas.tag_bind(button_text, "<Enter>",
                               lambda e: self.web_button_hover_enter(button_obj))
        self.bg_canvas.tag_bind(button_text, "<Leave>",
                               lambda e: self.web_button_hover_leave(button_obj))

        return button_obj

    def create_status_indicator(self):
        """Create status indicator exactly like web demo."""
        # Top right corner
        self.bg_canvas.create_rectangle(
            950, 20, 1180, 100,
            fill=self.colors["panel_bg"],
            outline=self.colors["neon_green"], width=2
        )

        self.bg_canvas.create_text(
            1065, 40,
            text="✅ EXHIBITION READY",
            font=("Arial", 12, "bold"),
            fill=self.colors["neon_green"],
            anchor="center"
        )

        self.bg_canvas.create_text(
            1065, 70,
            text="All enhancements active!",
            font=("Arial", 10),
            fill=self.colors["text_light"],
            anchor="center"
        )

    def start_animations(self):
        """Start all animations exactly like web demo."""
        self.animate_atoms()
        self.animate_title()
        self.animate_bonds()

    def animate_atoms(self):
        """Animate floating atoms exactly like CSS animation."""
        for atom in self.atoms:
            # Calculate animation progress (6 second cycle)
            progress = (self.animation_frame + atom["delay"] * 60) % 360

            # CSS-like float animation
            offset_x = 20 * math.sin(math.radians(progress))
            offset_y = 20 * math.cos(math.radians(progress * 0.5))

            new_x = atom["base_x"] + offset_x
            new_y = atom["base_y"] + offset_y

            # Update positions
            self.bg_canvas.coords(atom["outer"],
                                new_x - 8, new_y - 8, new_x + 8, new_y + 8)
            self.bg_canvas.coords(atom["inner"],
                                new_x - 5, new_y - 5, new_x + 5, new_y + 5)

            atom["x"] = new_x
            atom["y"] = new_y

        # Continue animation
        self.animation_frame += 1
        self.root.after(50, self.animate_atoms)

    def animate_title(self):
        """Animate title color changes exactly like CSS."""
        # Change color every 1 second (like CSS 4s cycle / 4 colors)
        self.current_title_color = (self.current_title_color + 1) % len(self.title_colors)
        new_color = self.title_colors[self.current_title_color]

        self.bg_canvas.itemconfig(self.title_text, fill=new_color)

        # Schedule next color change
        self.root.after(1000, self.animate_title)

    def animate_bonds(self):
        """Animate molecular bonds with pulsing effect."""
        # Pulse effect like CSS
        pulse = abs(math.sin(self.animation_frame * 0.1)) * 2 + 1

        for bond in self.bonds:
            self.bg_canvas.itemconfig(bond["id"], width=int(pulse))

        # Continue animation
        self.root.after(100, self.animate_bonds)

    def web_button_hover_enter(self, button):
        """Handle button hover enter exactly like CSS."""
        # Lighten color and add transform effect
        lighter_color = self.lighten_color(button["color"])
        self.bg_canvas.itemconfig(button["bg"], fill=lighter_color, outline=lighter_color)

        # Simulate transform: translateY(-3px) with shadow
        self.bg_canvas.move(button["bg"], 0, -3)
        self.bg_canvas.move(button["text"], 0, -3)
        for glow in button["glow"]:
            self.bg_canvas.move(glow, 0, -3)

    def web_button_hover_leave(self, button):
        """Handle button hover leave."""
        # Restore original color and position
        self.bg_canvas.itemconfig(button["bg"], fill=button["color"], outline=button["color"])

        # Restore position
        self.bg_canvas.move(button["bg"], 0, 3)
        self.bg_canvas.move(button["text"], 0, 3)
        for glow in button["glow"]:
            self.bg_canvas.move(glow, 0, 3)

    def web_button_click(self, button):
        """Handle button click exactly like web demo."""
        # Flash to gold color (like CSS active state)
        original_color = button["color"]
        self.bg_canvas.itemconfig(button["bg"], fill=self.colors["gold"], outline=self.colors["gold"])

        def restore_color():
            self.bg_canvas.itemconfig(button["bg"], fill=original_color, outline=original_color)

        # Restore after 150ms
        self.root.after(150, restore_color)

        # Handle button actions
        if "PLAY" in button["text_content"]:
            self.start_quiz()
        elif "DIFFICULTY" in button["text_content"]:
            self.change_difficulty()
        elif "SCORES" in button["text_content"]:
            self.show_high_scores()
        elif "HELP" in button["text_content"]:
            self.show_instructions()

    def create_enhanced_button(self, parent, text, command, color):
        """Create an enhanced button with visual effects."""
        def enhanced_command():
            self.play_sound_effect("click")
            self.flash_button(button, color)
            if command:
                command()

        def on_enter(event):
            lighter = self.lighten_color(color)
            button.config(bg=lighter)

        def on_leave(event):
            button.config(bg=color)

        button = tk.Button(
            parent,
            text=text,
            command=enhanced_command,
            font=("Arial", 14, "bold"),
            bg=color,
            fg="white",
            width=30,
            pady=12,
            bd=0,
            activebackground=color
        )

        button.bind("<Enter>", on_enter)
        button.bind("<Leave>", on_leave)

        return button

    def lighten_color(self, color):
        """Lighten a hex color."""
        color = color.lstrip('#')
        rgb = tuple(int(color[i:i+2], 16) for i in (0, 2, 4))
        lightened = tuple(min(255, int(c + (255-c)*0.3)) for c in rgb)
        return f"#{lightened[0]:02x}{lightened[1]:02x}{lightened[2]:02x}"

    def flash_button(self, button, original_color):
        """Create a flash effect for buttons."""
        button.config(bg=self.colors["gold"])
        self.root.after(100, lambda: button.config(bg=self.colors["silver"]))
        self.root.after(200, lambda: button.config(bg=original_color))

    def play_sound_effect(self, sound_name):
        """Play sound effect or provide visual feedback."""
        if PYGAME_AVAILABLE:
            try:
                # Try to play actual sound if available
                sound_file = f"assets/sounds/{sound_name}.wav"
                if os.path.exists(sound_file):
                    sound = pygame.mixer.Sound(sound_file)
                    sound.play()
                    return
            except:
                pass

        # Visual feedback instead of sound
        print(f"🔊 {sound_name.upper()} sound effect")

    def start_quiz(self):
        """Start the quiz."""
        self.score = 0
        self.questions_answered = 0
        self.correct_answers = 0
        self.show_question()

    def show_question(self):
        """Show a quiz question."""
        # Clear window
        for widget in self.root.winfo_children():
            widget.destroy()

        # Select random element
        element_numbers = list(ELEMENTS.keys())
        if self.current_difficulty == "easy":
            element_numbers = [n for n in element_numbers if n <= 10]
        elif self.current_difficulty == "medium":
            element_numbers = [n for n in element_numbers if n <= 20]

        if not element_numbers:
            element_numbers = list(ELEMENTS.keys())[:5]  # Fallback

        element_num = random.choice(element_numbers)
        element = ELEMENTS[element_num]

        self.current_question = {
            "element": element,
            "number": element_num,
            "answer": element["symbol"]
        }

        # Question interface
        main_frame = tk.Frame(self.root, bg=self.colors["bg_dark"])
        main_frame.pack(fill="both", expand=True)

        # Question counter
        counter = tk.Label(
            main_frame,
            text=f"Question {self.questions_answered + 1}/5",
            font=("Arial", 16, "bold"),
            bg=self.colors["bg_dark"],
            fg=self.colors["neon_blue"]
        )
        counter.pack(pady=20)

        # Score
        score_label = tk.Label(
            main_frame,
            text=f"Score: {self.score}",
            font=("Arial", 14),
            bg=self.colors["bg_dark"],
            fg=self.colors["gold"]
        )
        score_label.pack()

        # Question
        question = tk.Label(
            main_frame,
            text=f"What is the chemical symbol for {element['name']}?",
            font=("Arial", 20, "bold"),
            bg=self.colors["panel_bg"],
            fg=self.colors["text_light"],
            padx=40,
            pady=30
        )
        question.pack(pady=40)

        # Answer entry
        self.answer_var = tk.StringVar()
        answer_entry = tk.Entry(
            main_frame,
            textvariable=self.answer_var,
            font=("Arial", 16),
            width=10,
            justify="center"
        )
        answer_entry.pack(pady=20)
        answer_entry.focus_set()

        # Bind Enter key
        answer_entry.bind("<Return>", lambda e: self.check_answer())

        # Submit button
        submit_btn = self.create_enhanced_button(
            main_frame, "✅ SUBMIT ANSWER", self.check_answer,
            self.colors["neon_green"]
        )
        submit_btn.pack(pady=20)

    def check_answer(self):
        """Check the submitted answer."""
        user_answer = self.answer_var.get().strip().upper()
        correct_answer = self.current_question["answer"].upper()

        self.questions_answered += 1

        if user_answer == correct_answer:
            self.correct_answers += 1
            self.score += 10
            self.play_sound_effect("correct")
            self.show_result("✅ CORRECT!", self.colors["success"])
        else:
            self.play_sound_effect("incorrect")
            self.show_result(f"❌ INCORRECT!\nCorrect answer: {correct_answer}",
                           self.colors["error"])

    def show_result(self, message, color):
        """Show the result of the answer."""
        # Clear window
        for widget in self.root.winfo_children():
            widget.destroy()

        main_frame = tk.Frame(self.root, bg=self.colors["bg_dark"])
        main_frame.pack(fill="both", expand=True)

        # Result message
        result = tk.Label(
            main_frame,
            text=message,
            font=("Arial", 24, "bold"),
            bg=self.colors["bg_dark"],
            fg=color,
            justify="center"
        )
        result.pack(pady=100)

        # Continue or finish
        if self.questions_answered < 5:
            next_btn = self.create_enhanced_button(
                main_frame, "➡️ NEXT QUESTION", self.show_question,
                self.colors["neon_blue"]
            )
            next_btn.pack(pady=20)
        else:
            self.finish_quiz()

    def finish_quiz(self):
        """Finish the quiz and show results."""
        # Update high score
        if self.score > self.high_scores[self.current_difficulty]:
            self.high_scores[self.current_difficulty] = self.score
            self.save_high_scores()
            new_record = True
        else:
            new_record = False

        # Clear window
        for widget in self.root.winfo_children():
            widget.destroy()

        main_frame = tk.Frame(self.root, bg=self.colors["bg_dark"])
        main_frame.pack(fill="both", expand=True)

        # Final score
        title = tk.Label(
            main_frame,
            text="🎉 QUIZ COMPLETE! 🎉",
            font=("Arial", 28, "bold"),
            bg=self.colors["bg_dark"],
            fg=self.colors["gold"]
        )
        title.pack(pady=40)

        score_text = f"Final Score: {self.score}/50\nCorrect Answers: {self.correct_answers}/5"
        if new_record:
            score_text += f"\n🏆 NEW HIGH SCORE! 🏆"

        score = tk.Label(
            main_frame,
            text=score_text,
            font=("Arial", 18),
            bg=self.colors["panel_bg"],
            fg=self.colors["text_light"],
            padx=40,
            pady=30,
            justify="center"
        )
        score.pack(pady=30)

        # Buttons
        self.create_enhanced_button(
            main_frame, "🔄 PLAY AGAIN", self.start_quiz,
            self.colors["neon_green"]
        ).pack(pady=10)

        self.create_enhanced_button(
            main_frame, "🏠 MAIN MENU", self.create_main_interface,
            self.colors["neon_blue"]
        ).pack(pady=10)

    def change_difficulty(self):
        """Change difficulty level."""
        difficulties = ["easy", "medium", "hard"]
        current_index = difficulties.index(self.current_difficulty)
        next_index = (current_index + 1) % len(difficulties)
        self.current_difficulty = difficulties[next_index]
        self.create_main_interface()

    def show_high_scores(self):
        """Show high scores."""
        messagebox.showinfo("🏆 High Scores",
                          f"Easy: {self.high_scores['easy']}\n" +
                          f"Medium: {self.high_scores['medium']}\n" +
                          f"Hard: {self.high_scores['hard']}")

    def show_instructions(self):
        """Show game instructions."""
        messagebox.showinfo("❓ How to Play",
                          "🧪 PERIODIC TABLE QUEST\n\n" +
                          "1. Choose your difficulty level\n" +
                          "2. Answer 5 questions about elements\n" +
                          "3. Type the chemical symbol for each element\n" +
                          "4. Try to beat your high score!\n\n" +
                          "✨ Enhanced with visual effects and animations!")

if __name__ == "__main__":
    print("🧪 STARTING FIXED PERIODIC TABLE QUEST")
    print("=" * 50)
    print("✅ All pygame errors fixed!")
    print("✅ Safe library handling implemented!")
    print("✅ Exhibition ready!")
    print("=" * 50)

    root = tk.Tk()
    app = PeriodicTableQuestFixed(root)

    print("🚀 Fixed version launched successfully!")

    root.mainloop()

    print("👋 Fixed version closed successfully!")
