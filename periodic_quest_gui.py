"""
Periodic Table Quest - Enhanced GUI Version
A quiz game to test your knowledge of the periodic table with a visually engaging interface.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import random
import time
import json
import os
import threading
from periodic_data import ELEMENTS, DIFFICULTY_LEVELS, ELEMENT_GROUPS

class PeriodicTableQuestGUI:
    """Enhanced GUI version of the Periodic Table Quest game."""

    def __init__(self, root):
        """Initialize the GUI application."""
        self.root = root
        self.root.title("🧪 Periodic Table Quest")
        self.root.geometry("900x700")
        self.root.resizable(True, True)

        # Define color scheme - dark theme with neon accents
        self.colors = {
            "bg_dark": "#121212",  # Dark background
            "panel_bg": "#1E1E1E",  # Slightly lighter panel background
            "text_light": "#E0E0E0",  # Light text color
            "neon_blue": "#00BFFF",  # Bright blue
            "neon_green": "#39FF14",  # Bright green
            "neon_purple": "#9D00FF",  # Bright purple
            "neon_pink": "#FF00FF",  # Bright pink
            "warning": "#FF5722",  # Orange for warnings/timer
            "success": "#00E676",  # Green for success
            "error": "#FF1744",  # Red for errors
            "neutral": "#607D8B"  # Neutral blue-gray
        }

        # Configure root with dark theme
        self.root.configure(bg=self.colors["bg_dark"])

        # Game variables
        self.score = 0
        self.high_scores = self.load_high_scores()
        self.current_difficulty = "easy"
        self.question_time_limit = 30  # seconds
        self.hints_used = 0
        self.timer_running = False
        self.remaining_time = 0
        self.current_question = None
        self.questions_in_round = 5
        self.current_question_num = 0
        self.correct_answers = 0

        # Create frames
        self.create_frames()

        # Show main menu
        self.show_main_menu()

    def create_frames(self):
        """Create the main frames for the application."""
        # Main menu frame
        self.menu_frame = tk.Frame(self.root, bg=self.colors["bg_dark"])

        # Game frame
        self.game_frame = tk.Frame(self.root, bg=self.colors["bg_dark"])

        # Results frame
        self.results_frame = tk.Frame(self.root, bg=self.colors["bg_dark"])

    def load_high_scores(self):
        """Load high scores from file if it exists."""
        if os.path.exists("high_scores.json"):
            try:
                with open("high_scores.json", "r") as file:
                    return json.load(file)
            except:
                return {"easy": 0, "medium": 0, "hard": 0}
        else:
            return {"easy": 0, "medium": 0, "hard": 0}

    def save_high_scores(self):
        """Save high scores to file."""
        with open("high_scores.json", "w") as file:
            json.dump(self.high_scores, file)

    def update_high_score(self):
        """Update high score if current score is higher."""
        if self.score > self.high_scores[self.current_difficulty]:
            self.high_scores[self.current_difficulty] = self.score
            self.save_high_scores()
            return True
        return False

    def set_difficulty(self, difficulty):
        """Set the game difficulty level."""
        if difficulty in DIFFICULTY_LEVELS:
            self.current_difficulty = difficulty
            # Adjust time limit based on difficulty
            if difficulty == "easy":
                self.question_time_limit = 30
            elif difficulty == "medium":
                self.question_time_limit = 25
            else:  # hard
                self.question_time_limit = 20
            return True
        return False

    def show_main_menu(self):
        """Display the main menu."""
        # Hide other frames
        self.game_frame.pack_forget()
        self.results_frame.pack_forget()

        # Clear menu frame
        for widget in self.menu_frame.winfo_children():
            widget.destroy()

        # Create a panel frame for content
        panel = tk.Frame(
            self.menu_frame,
            bg=self.colors["panel_bg"],
            padx=40,
            pady=40,
            bd=0,
            highlightthickness=2,
            highlightbackground=self.colors["neon_blue"]
        )

        # Title
        title_label = tk.Label(
            panel,
            text="🧪 PERIODIC TABLE QUEST 🧪",
            font=("Orbitron", 28, "bold"),
            bg=self.colors["panel_bg"],
            fg=self.colors["neon_green"]
        )
        title_label.pack(pady=30)

        # Subtitle
        subtitle_label = tk.Label(
            panel,
            text="Test your knowledge of the elements!",
            font=("Orbitron", 14),
            bg=self.colors["panel_bg"],
            fg=self.colors["text_light"]
        )
        subtitle_label.pack(pady=(0, 30))

        # Button hover effect functions
        def on_enter(e, button, color):
            button.config(bg=color)

        def on_leave(e, button, color):
            button.config(bg=color)

        # Buttons with neon colors
        button_style = {
            "font": ("Orbitron", 14),
            "width": 20,
            "pady": 12,
            "bd": 0,
            "fg": self.colors["text_light"]
        }

        # Play button - neon green
        play_button = tk.Button(
            panel,
            text="PLAY GAME",
            command=self.start_game,
            bg=self.colors["neon_green"],
            activebackground=self.colors["neon_green"],
            **button_style
        )
        play_button.pack(pady=10)
        play_button.bind("<Enter>", lambda e: on_enter(e, play_button, "#50FF50"))
        play_button.bind("<Leave>", lambda e: on_leave(e, play_button, self.colors["neon_green"]))

        # Difficulty button - neon blue
        difficulty_button = tk.Button(
            panel,
            text=f"DIFFICULTY: {self.current_difficulty.upper()}",
            command=self.show_difficulty_menu,
            bg=self.colors["neon_blue"],
            activebackground=self.colors["neon_blue"],
            **button_style
        )
        difficulty_button.pack(pady=10)
        difficulty_button.bind("<Enter>", lambda e: on_enter(e, difficulty_button, "#50CFFF"))
        difficulty_button.bind("<Leave>", lambda e: on_leave(e, difficulty_button, self.colors["neon_blue"]))

        # High scores button - neon purple
        high_scores_button = tk.Button(
            panel,
            text="HIGH SCORES",
            command=self.show_high_scores,
            bg=self.colors["neon_purple"],
            activebackground=self.colors["neon_purple"],
            **button_style
        )
        high_scores_button.pack(pady=10)
        high_scores_button.bind("<Enter>", lambda e: on_enter(e, high_scores_button, "#B050FF"))
        high_scores_button.bind("<Leave>", lambda e: on_leave(e, high_scores_button, self.colors["neon_purple"]))

        # Instructions button - neon pink
        instructions_button = tk.Button(
            panel,
            text="HOW TO PLAY",
            command=self.show_instructions,
            bg=self.colors["neon_pink"],
            activebackground=self.colors["neon_pink"],
            **button_style
        )
        instructions_button.pack(pady=10)
        instructions_button.bind("<Enter>", lambda e: on_enter(e, instructions_button, "#FF50FF"))
        instructions_button.bind("<Leave>", lambda e: on_leave(e, instructions_button, self.colors["neon_pink"]))

        # Exit button - neutral color
        exit_button = tk.Button(
            panel,
            text="EXIT",
            command=self.root.quit,
            bg=self.colors["neutral"],
            activebackground=self.colors["neutral"],
            **button_style
        )
        exit_button.pack(pady=10)
        exit_button.bind("<Enter>", lambda e: on_enter(e, exit_button, "#78909C"))
        exit_button.bind("<Leave>", lambda e: on_leave(e, exit_button, self.colors["neutral"]))

        # Center the panel in the frame
        panel.pack(expand=True, pady=50)

        # Show menu frame
        self.menu_frame.pack(fill="both", expand=True)

    def show_difficulty_menu(self):
        """Show the difficulty selection menu."""
        # Clear menu frame
        for widget in self.menu_frame.winfo_children():
            widget.destroy()

        # Create a panel frame for content
        panel = tk.Frame(
            self.menu_frame,
            bg=self.colors["panel_bg"],
            padx=40,
            pady=40,
            bd=0,
            highlightthickness=2,
            highlightbackground=self.colors["neon_blue"]
        )

        # Title
        title_label = tk.Label(
            panel,
            text="SELECT DIFFICULTY",
            font=("Orbitron", 28, "bold"),
            bg=self.colors["panel_bg"],
            fg=self.colors["neon_blue"]
        )
        title_label.pack(pady=30)

        # Subtitle
        subtitle_label = tk.Label(
            panel,
            text="Choose your challenge level",
            font=("Orbitron", 14),
            bg=self.colors["panel_bg"],
            fg=self.colors["text_light"]
        )
        subtitle_label.pack(pady=(0, 30))

        # Difficulty descriptions
        descriptions = {
            "easy": "Elements 1-20 (Hydrogen to Calcium)",
            "medium": "Elements 1-40 (Hydrogen to Zirconium)",
            "hard": "Elements 1-60 (Hydrogen to Neodymium)"
        }

        # Button hover effect functions
        def on_enter(e, button, color):
            button.config(bg=color)

        def on_leave(e, button, color):
            button.config(bg=color)

        # Buttons
        button_style = {
            "font": ("Orbitron", 14),
            "width": 35,
            "pady": 12,
            "bd": 0,
            "fg": self.colors["text_light"],
            "anchor": "w"
        }

        # Easy button
        easy_color = self.colors["neon_green"]
        easy_button = tk.Button(
            panel,
            text=f"⚛️ EASY - {descriptions['easy']}",
            command=lambda: self.set_difficulty_and_return("easy"),
            bg=easy_color,
            activebackground=easy_color,
            **button_style
        )
        easy_button.pack(pady=10)
        easy_button.bind("<Enter>", lambda e: on_enter(e, easy_button, "#50FF50"))
        easy_button.bind("<Leave>", lambda e: on_leave(e, easy_button, easy_color))

        # Medium button
        medium_color = self.colors["neon_blue"]
        medium_button = tk.Button(
            panel,
            text=f"⚛️ MEDIUM - {descriptions['medium']}",
            command=lambda: self.set_difficulty_and_return("medium"),
            bg=medium_color,
            activebackground=medium_color,
            **button_style
        )
        medium_button.pack(pady=10)
        medium_button.bind("<Enter>", lambda e: on_enter(e, medium_button, "#50CFFF"))
        medium_button.bind("<Leave>", lambda e: on_leave(e, medium_button, medium_color))

        # Hard button
        hard_color = self.colors["neon_purple"]
        hard_button = tk.Button(
            panel,
            text=f"⚛️ HARD - {descriptions['hard']}",
            command=lambda: self.set_difficulty_and_return("hard"),
            bg=hard_color,
            activebackground=hard_color,
            **button_style
        )
        hard_button.pack(pady=10)
        hard_button.bind("<Enter>", lambda e: on_enter(e, hard_button, "#B050FF"))
        hard_button.bind("<Leave>", lambda e: on_leave(e, hard_button, hard_color))

        # Back button
        back_button = tk.Button(
            panel,
            text="← BACK TO MAIN MENU",
            command=self.show_main_menu,
            bg=self.colors["neutral"],
            activebackground=self.colors["neutral"],
            **button_style
        )
        back_button.pack(pady=20)
        back_button.bind("<Enter>", lambda e: on_enter(e, back_button, "#78909C"))
        back_button.bind("<Leave>", lambda e: on_leave(e, back_button, self.colors["neutral"]))

        # Center the panel in the frame
        panel.pack(expand=True, pady=50)

    def set_difficulty_and_return(self, difficulty):
        """Set the difficulty and return to the main menu."""
        self.set_difficulty(difficulty)
        self.show_main_menu()

    def show_high_scores(self):
        """Show the high scores screen."""
        # Clear menu frame
        for widget in self.menu_frame.winfo_children():
            widget.destroy()

        # Create a panel frame for content
        panel = tk.Frame(
            self.menu_frame,
            bg=self.colors["panel_bg"],
            padx=40,
            pady=40,
            bd=0,
            highlightthickness=2,
            highlightbackground=self.colors["neon_purple"]
        )

        # Title
        title_label = tk.Label(
            panel,
            text="HIGH SCORES",
            font=("Orbitron", 28, "bold"),
            bg=self.colors["panel_bg"],
            fg=self.colors["neon_purple"]
        )
        title_label.pack(pady=30)

        # Subtitle
        subtitle_label = tk.Label(
            panel,
            text="Your best performances",
            font=("Orbitron", 14),
            bg=self.colors["panel_bg"],
            fg=self.colors["text_light"]
        )
        subtitle_label.pack(pady=(0, 30))

        # Scores frame with a futuristic border
        scores_frame = tk.Frame(
            panel,
            bg=self.colors["panel_bg"],
            highlightbackground=self.colors["neon_blue"],
            highlightthickness=1,
            padx=20,
            pady=20
        )
        scores_frame.pack(pady=20)

        # Headers
        tk.Label(
            scores_frame,
            text="DIFFICULTY",
            font=("Orbitron", 16, "bold"),
            bg=self.colors["panel_bg"],
            fg=self.colors["neon_blue"],
            width=15
        ).grid(row=0, column=0, padx=10, pady=10)

        tk.Label(
            scores_frame,
            text="SCORE",
            font=("Orbitron", 16, "bold"),
            bg=self.colors["panel_bg"],
            fg=self.colors["neon_blue"],
            width=10
        ).grid(row=0, column=1, padx=10, pady=10)

        # Easy score
        tk.Label(
            scores_frame,
            text="EASY",
            font=("Orbitron", 14),
            bg=self.colors["panel_bg"],
            fg=self.colors["neon_green"],
            width=15
        ).grid(row=1, column=0, padx=10, pady=10)

        tk.Label(
            scores_frame,
            text=str(self.high_scores["easy"]),
            font=("Orbitron", 14, "bold"),
            bg=self.colors["panel_bg"],
            fg=self.colors["text_light"],
            width=10
        ).grid(row=1, column=1, padx=10, pady=10)

        # Medium score
        tk.Label(
            scores_frame,
            text="MEDIUM",
            font=("Orbitron", 14),
            bg=self.colors["panel_bg"],
            fg=self.colors["neon_blue"],
            width=15
        ).grid(row=2, column=0, padx=10, pady=10)

        tk.Label(
            scores_frame,
            text=str(self.high_scores["medium"]),
            font=("Orbitron", 14, "bold"),
            bg=self.colors["panel_bg"],
            fg=self.colors["text_light"],
            width=10
        ).grid(row=2, column=1, padx=10, pady=10)

        # Hard score
        tk.Label(
            scores_frame,
            text="HARD",
            font=("Orbitron", 14),
            bg=self.colors["panel_bg"],
            fg=self.colors["neon_purple"],
            width=15
        ).grid(row=3, column=0, padx=10, pady=10)

        tk.Label(
            scores_frame,
            text=str(self.high_scores["hard"]),
            font=("Orbitron", 14, "bold"),
            bg=self.colors["panel_bg"],
            fg=self.colors["text_light"],
            width=10
        ).grid(row=3, column=1, padx=10, pady=10)

        # Button hover effect functions
        def on_enter(e, button, color):
            button.config(bg=color)

        def on_leave(e, button, color):
            button.config(bg=color)

        # Back button
        back_button = tk.Button(
            panel,
            text="← BACK TO MAIN MENU",
            command=self.show_main_menu,
            font=("Orbitron", 14),
            width=20,
            pady=10,
            bg=self.colors["neutral"],
            activebackground=self.colors["neutral"],
            fg=self.colors["text_light"],
            bd=0
        )
        back_button.pack(pady=20)
        back_button.bind("<Enter>", lambda e: on_enter(e, back_button, "#78909C"))
        back_button.bind("<Leave>", lambda e: on_leave(e, back_button, self.colors["neutral"]))

        # Center the panel in the frame
        panel.pack(expand=True, pady=50)

    def show_instructions(self):
        """Show the game instructions."""
        # Clear menu frame
        for widget in self.menu_frame.winfo_children():
            widget.destroy()

        # Create a panel frame for content
        panel = tk.Frame(
            self.menu_frame,
            bg=self.colors["panel_bg"],
            padx=40,
            pady=40,
            bd=0,
            highlightthickness=2,
            highlightbackground=self.colors["neon_pink"]
        )

        # Title
        title_label = tk.Label(
            panel,
            text="HOW TO PLAY",
            font=("Orbitron", 28, "bold"),
            bg=self.colors["panel_bg"],
            fg=self.colors["neon_pink"]
        )
        title_label.pack(pady=20)

        # Instructions frame
        instructions_frame = tk.Frame(
            panel,
            bg=self.colors["panel_bg"],
            highlightbackground=self.colors["neon_blue"],
            highlightthickness=1,
            padx=30,
            pady=20
        )
        instructions_frame.pack(pady=20, fill="both", expand=True)

        # Step 1
        step1_frame = tk.Frame(instructions_frame, bg=self.colors["panel_bg"])
        step1_frame.pack(anchor="w", pady=5, fill="x")

        step1_num = tk.Label(
            step1_frame,
            text="1",
            font=("Orbitron", 16, "bold"),
            bg=self.colors["neon_green"],
            fg=self.colors["panel_bg"],
            width=2,
            height=1
        )
        step1_num.pack(side="left", padx=(0, 10))

        step1_text = tk.Label(
            step1_frame,
            text="Select a difficulty level (Easy, Medium, Hard)",
            font=("Orbitron", 14),
            bg=self.colors["panel_bg"],
            fg=self.colors["text_light"],
            justify="left"
        )
        step1_text.pack(side="left", anchor="w")

        # Step 2
        step2_frame = tk.Frame(instructions_frame, bg=self.colors["panel_bg"])
        step2_frame.pack(anchor="w", pady=5, fill="x")

        step2_num = tk.Label(
            step2_frame,
            text="2",
            font=("Orbitron", 16, "bold"),
            bg=self.colors["neon_blue"],
            fg=self.colors["panel_bg"],
            width=2,
            height=1
        )
        step2_num.pack(side="left", padx=(0, 10))

        step2_text = tk.Label(
            step2_frame,
            text="Answer questions about the periodic table",
            font=("Orbitron", 14),
            bg=self.colors["panel_bg"],
            fg=self.colors["text_light"],
            justify="left"
        )
        step2_text.pack(side="left", anchor="w")

        # Step 3
        step3_frame = tk.Frame(instructions_frame, bg=self.colors["panel_bg"])
        step3_frame.pack(anchor="w", pady=5, fill="x")

        step3_num = tk.Label(
            step3_frame,
            text="3",
            font=("Orbitron", 16, "bold"),
            bg=self.colors["neon_purple"],
            fg=self.colors["panel_bg"],
            width=2,
            height=1
        )
        step3_num.pack(side="left", padx=(0, 10))

        step3_text = tk.Label(
            step3_frame,
            text="Click the 'Hint' button if you need help (reduces points)",
            font=("Orbitron", 14),
            bg=self.colors["panel_bg"],
            fg=self.colors["text_light"],
            justify="left"
        )
        step3_text.pack(side="left", anchor="w")

        # Step 4
        step4_frame = tk.Frame(instructions_frame, bg=self.colors["panel_bg"])
        step4_frame.pack(anchor="w", pady=5, fill="x")

        step4_num = tk.Label(
            step4_frame,
            text="4",
            font=("Orbitron", 16, "bold"),
            bg=self.colors["neon_pink"],
            fg=self.colors["panel_bg"],
            width=2,
            height=1
        )
        step4_num.pack(side="left", padx=(0, 10))

        step4_text = tk.Label(
            step4_frame,
            text="Answer quickly for more points!",
            font=("Orbitron", 14),
            bg=self.colors["panel_bg"],
            fg=self.colors["text_light"],
            justify="left"
        )
        step4_text.pack(side="left", anchor="w")

        # Step 5
        step5_frame = tk.Frame(instructions_frame, bg=self.colors["panel_bg"])
        step5_frame.pack(anchor="w", pady=5, fill="x")

        step5_num = tk.Label(
            step5_frame,
            text="5",
            font=("Orbitron", 16, "bold"),
            bg=self.colors["warning"],
            fg=self.colors["panel_bg"],
            width=2,
            height=1
        )
        step5_num.pack(side="left", padx=(0, 10))

        step5_text = tk.Label(
            step5_frame,
            text="Try to beat your high score!",
            font=("Orbitron", 14),
            bg=self.colors["panel_bg"],
            fg=self.colors["text_light"],
            justify="left"
        )
        step5_text.pack(side="left", anchor="w")

        # Question types section
        question_title = tk.Label(
            instructions_frame,
            text="QUESTION TYPES:",
            font=("Orbitron", 16, "bold"),
            bg=self.colors["panel_bg"],
            fg=self.colors["neon_blue"],
            justify="left"
        )
        question_title.pack(anchor="w", pady=(20, 10))

        # Question type bullets
        question_types = [
            "Element symbols and names",
            "Atomic numbers",
            "Element groups"
        ]

        for q_type in question_types:
            q_frame = tk.Frame(instructions_frame, bg=self.colors["panel_bg"])
            q_frame.pack(anchor="w", pady=2, fill="x")

            bullet = tk.Label(
                q_frame,
                text="•",
                font=("Orbitron", 16),
                bg=self.colors["panel_bg"],
                fg=self.colors["neon_green"],
                width=2
            )
            bullet.pack(side="left")

            q_text = tk.Label(
                q_frame,
                text=q_type,
                font=("Orbitron", 14),
                bg=self.colors["panel_bg"],
                fg=self.colors["text_light"],
                justify="left"
            )
            q_text.pack(side="left", anchor="w")

        # Good luck message
        good_luck = tk.Label(
            panel,
            text="GOOD LUCK, SCIENTIST!",
            font=("Orbitron", 18, "bold"),
            bg=self.colors["panel_bg"],
            fg=self.colors["neon_green"],
            pady=10
        )
        good_luck.pack(pady=20)

        # Button hover effect functions
        def on_enter(e, button, color):
            button.config(bg=color)

        def on_leave(e, button, color):
            button.config(bg=color)

        # Back button
        back_button = tk.Button(
            panel,
            text="← BACK TO MAIN MENU",
            command=self.show_main_menu,
            font=("Orbitron", 14),
            width=20,
            pady=10,
            bg=self.colors["neutral"],
            activebackground=self.colors["neutral"],
            fg=self.colors["text_light"],
            bd=0
        )
        back_button.pack(pady=10)
        back_button.bind("<Enter>", lambda e: on_enter(e, back_button, "#78909C"))
        back_button.bind("<Leave>", lambda e: on_leave(e, back_button, self.colors["neutral"]))

        # Center the panel in the frame
        panel.pack(expand=True, pady=30)

    def start_game(self):
        """Start a new game."""
        # Reset game variables
        self.score = 0
        self.hints_used = 0
        self.current_question_num = 0
        self.correct_answers = 0

        # Hide menu frame
        self.menu_frame.pack_forget()

        # Clear game frame
        for widget in self.game_frame.winfo_children():
            widget.destroy()

        # Show game frame
        self.game_frame.pack(fill="both", expand=True)

        # Start first question
        self.next_question()

    def generate_question(self):
        """Generate a random question based on current difficulty."""
        # Get elements available for the current difficulty
        available_elements = DIFFICULTY_LEVELS[self.current_difficulty]

        # Choose a random element
        element_number = random.choice(available_elements)
        element = ELEMENTS[element_number]

        # Choose a question type
        question_types = [
            "symbol_to_name",  # What element has the symbol X?
            "name_to_symbol",  # What is the symbol for X?
            "number_to_element",  # What element has atomic number X?
            "element_to_number",  # What is the atomic number of X?
            "group_to_element",  # Name an element in the X group
        ]

        question_type = random.choice(question_types)

        if question_type == "symbol_to_name":
            question = f"What element has the symbol '{element['symbol']}'?"
            answer = element['name']
            hint = f"This element has atomic number {element_number}."

        elif question_type == "name_to_symbol":
            question = f"What is the symbol for {element['name']}?"
            answer = element['symbol']
            hint = f"This element has atomic number {element_number}."

        elif question_type == "number_to_element":
            question = f"What element has atomic number {element_number}?"
            answer = element['name']
            hint = f"The symbol for this element is '{element['symbol']}'."

        elif question_type == "element_to_number":
            question = f"What is the atomic number of {element['name']}?"
            answer = str(element_number)
            hint = f"The symbol for this element is '{element['symbol']}'."

        elif question_type == "group_to_element":
            group = element['group']
            question = f"Name an element in the {group} group."
            # Any element in this group is a valid answer
            group_elements = [ELEMENTS[num]['name'] for num in ELEMENT_GROUPS.get(group, [])]
            answer = group_elements  # List of valid answers
            hint = f"There are {len(group_elements)} elements in this group."

        return {
            "question": question,
            "answer": answer,
            "hint": hint,
            "fact": element['fact'],
            "element_number": element_number
        }

    def next_question(self):
        """Display the next question."""
        self.current_question_num += 1

        if self.current_question_num > self.questions_in_round:
            # End of round
            self.show_results()
            return

        # Clear game frame
        for widget in self.game_frame.winfo_children():
            widget.destroy()

        # Generate new question
        self.current_question = self.generate_question()

        # Create question UI
        self.create_question_ui()

        # Start timer
        self.remaining_time = self.question_time_limit
        self.timer_running = True
        self.update_timer()

    def create_question_ui(self):
        """Create the UI for the current question."""
        # Create a panel frame for content
        panel = tk.Frame(
            self.game_frame,
            bg=self.colors["panel_bg"],
            padx=40,
            pady=40,
            bd=0,
            highlightthickness=2,
            highlightbackground=self.colors["neon_blue"]
        )

        # Top info bar
        info_frame = tk.Frame(panel, bg=self.colors["panel_bg"])
        info_frame.pack(fill="x", pady=(0, 20))

        # Left side - Progress and difficulty
        left_info = tk.Frame(info_frame, bg=self.colors["panel_bg"])
        left_info.pack(side="left", fill="x", expand=True)

        # Progress label with atom icon
        progress_label = tk.Label(
            left_info,
            text=f"⚛️ Q{self.current_question_num}/{self.questions_in_round}",
            font=("Orbitron", 14, "bold"),
            bg=self.colors["panel_bg"],
            fg=self.colors["neon_blue"]
        )
        progress_label.pack(side="left", padx=10)

        # Difficulty label
        difficulty_colors = {
            "easy": self.colors["neon_green"],
            "medium": self.colors["neon_blue"],
            "hard": self.colors["neon_purple"]
        }

        difficulty_label = tk.Label(
            left_info,
            text=f"LEVEL: {self.current_difficulty.upper()}",
            font=("Orbitron", 14, "bold"),
            bg=self.colors["panel_bg"],
            fg=difficulty_colors[self.current_difficulty]
        )
        difficulty_label.pack(side="left", padx=10)

        # Right side - Score
        right_info = tk.Frame(info_frame, bg=self.colors["panel_bg"])
        right_info.pack(side="right")

        # Score label with digital counter style
        score_frame = tk.Frame(
            right_info,
            bg=self.colors["bg_dark"],
            highlightbackground=self.colors["neon_green"],
            highlightthickness=1,
            padx=10,
            pady=5
        )
        score_frame.pack(side="right")

        score_label = tk.Label(
            score_frame,
            text=f"SCORE: {self.score}",
            font=("Orbitron", 14, "bold"),
            bg=self.colors["bg_dark"],
            fg=self.colors["neon_green"]
        )
        score_label.pack()

        # Timer section
        timer_frame = tk.Frame(panel, bg=self.colors["panel_bg"])
        timer_frame.pack(fill="x", pady=(0, 20))

        # Timer label
        self.timer_label = tk.Label(
            timer_frame,
            text=f"TIME: {self.remaining_time}s",
            font=("Orbitron", 14, "bold"),
            bg=self.colors["panel_bg"],
            fg=self.colors["warning"]
        )
        self.timer_label.pack(side="left", padx=(0, 10))

        # Progress bar for timer
        self.timer_progress = ttk.Progressbar(
            timer_frame,
            orient="horizontal",
            length=400,
            mode="determinate",
            maximum=self.question_time_limit,
            value=self.remaining_time
        )
        self.timer_progress.pack(side="left", fill="x", expand=True)

        # Style the progress bar
        style = ttk.Style()
        style.theme_use('default')
        style.configure(
            "Horizontal.TProgressbar",
            troughcolor=self.colors["bg_dark"],
            background=self.colors["warning"],
            thickness=20
        )

        # Question section with lab report style
        question_frame = tk.Frame(
            panel,
            bg=self.colors["bg_dark"],
            padx=30,
            pady=30,
            highlightbackground=self.colors["neon_blue"],
            highlightthickness=2
        )
        question_frame.pack(fill="x", pady=20)

        # Lab report header
        lab_header = tk.Label(
            question_frame,
            text="LAB QUESTION:",
            font=("Orbitron", 14),
            bg=self.colors["bg_dark"],
            fg=self.colors["neon_blue"]
        )
        lab_header.pack(anchor="w", pady=(0, 10))

        # Question label
        question_label = tk.Label(
            question_frame,
            text=self.current_question["question"],
            font=("Orbitron", 18, "bold"),
            bg=self.colors["bg_dark"],
            fg=self.colors["text_light"],
            wraplength=700,
            justify="left"
        )
        question_label.pack(fill="x", pady=10)

        # Answer section
        answer_frame = tk.Frame(panel, bg=self.colors["panel_bg"], pady=20)
        answer_frame.pack(fill="x")

        answer_label = tk.Label(
            answer_frame,
            text="YOUR ANSWER:",
            font=("Orbitron", 14, "bold"),
            bg=self.colors["panel_bg"],
            fg=self.colors["neon_green"]
        )
        answer_label.pack(anchor="w", pady=(0, 10))

        # Entry with custom styling
        entry_frame = tk.Frame(
            answer_frame,
            bg=self.colors["neon_green"],
            padx=2,
            pady=2
        )
        entry_frame.pack(fill="x")

        self.answer_entry = tk.Entry(
            entry_frame,
            font=("Orbitron", 14),
            bg=self.colors["bg_dark"],
            fg=self.colors["text_light"],
            insertbackground=self.colors["neon_green"],  # Cursor color
            width=40,
            bd=0
        )
        self.answer_entry.pack(fill="x", ipady=8)
        self.answer_entry.focus_set()

        # Bind Enter key to submit answer
        self.answer_entry.bind("<Return>", lambda event: self.check_answer())

        # Buttons frame
        buttons_frame = tk.Frame(panel, bg=self.colors["panel_bg"])
        buttons_frame.pack(pady=20)

        # Button hover effect functions
        def on_enter(e, button, color):
            button.config(bg=color)

        def on_leave(e, button, color):
            button.config(bg=color)

        # Submit button
        submit_button = tk.Button(
            buttons_frame,
            text="SUBMIT ANSWER",
            command=self.check_answer,
            font=("Orbitron", 14, "bold"),
            width=15,
            pady=10,
            bg=self.colors["neon_green"],
            activebackground=self.colors["neon_green"],
            fg=self.colors["bg_dark"],
            bd=0
        )
        submit_button.grid(row=0, column=0, padx=10)
        submit_button.bind("<Enter>", lambda e: on_enter(e, submit_button, "#50FF50"))
        submit_button.bind("<Leave>", lambda e: on_leave(e, submit_button, self.colors["neon_green"]))

        # Hint button
        self.hint_button = tk.Button(
            buttons_frame,
            text="GET HINT",
            command=self.show_hint,
            font=("Orbitron", 14, "bold"),
            width=15,
            pady=10,
            bg=self.colors["warning"],
            activebackground=self.colors["warning"],
            fg=self.colors["bg_dark"],
            bd=0
        )
        self.hint_button.grid(row=0, column=1, padx=10)
        self.hint_button.bind("<Enter>", lambda e: on_enter(e, self.hint_button, "#FF7043"))
        self.hint_button.bind("<Leave>", lambda e: on_leave(e, self.hint_button, self.colors["warning"]))

        # Hint label (initially hidden)
        self.hint_label = tk.Label(
            panel,
            text="",
            font=("Orbitron", 14, "italic"),
            bg=self.colors["panel_bg"],
            fg=self.colors["warning"],
            wraplength=700,
            justify="left"
        )
        self.hint_label.pack(pady=10)

        # Result label (initially hidden)
        self.result_label = tk.Label(
            panel,
            text="",
            font=("Orbitron", 16, "bold"),
            bg=self.colors["panel_bg"],
            wraplength=700
        )
        self.result_label.pack(pady=10)

        # Next button (initially hidden)
        self.next_button = tk.Button(
            panel,
            text="NEXT QUESTION →",
            command=self.next_question,
            font=("Orbitron", 14, "bold"),
            width=20,
            pady=10,
            bg=self.colors["neon_blue"],
            activebackground=self.colors["neon_blue"],
            fg=self.colors["bg_dark"],
            bd=0
        )
        self.next_button.pack(pady=10)
        self.next_button.pack_forget()  # Hide initially

        # Pack the main panel
        panel.pack(expand=True, fill="both", padx=20, pady=20)

    def update_timer(self):
        """Update the timer display."""
        if not self.timer_running:
            return

        if self.remaining_time <= 0:
            # Time's up
            self.timer_running = False
            self.timer_label.config(text="TIME'S UP!")
            self.timer_progress["value"] = 0
            self.time_up()
            return

        # Update timer label
        self.timer_label.config(text=f"TIME: {self.remaining_time}s")

        # Update progress bar
        self.timer_progress["value"] = self.remaining_time

        # Change color based on time remaining
        if self.remaining_time <= 5:
            self.timer_label.config(fg=self.colors["error"])  # Red
            style = ttk.Style()
            style.configure("Horizontal.TProgressbar", background=self.colors["error"])
        elif self.remaining_time <= 10:
            self.timer_label.config(fg=self.colors["warning"])  # Orange
            style = ttk.Style()
            style.configure("Horizontal.TProgressbar", background=self.colors["warning"])

        # Decrement time
        self.remaining_time -= 1

        # Schedule next update
        self.root.after(1000, self.update_timer)

    def show_hint(self):
        """Show a hint for the current question."""
        if not self.hint_label.cget("text"):
            # Create a hint box with a lab note style
            hint_text = f"HINT: {self.current_question['hint']}"
            self.hint_label.config(text=hint_text)
            self.hints_used += 1
            self.hint_button.config(state="disabled")

    def check_answer(self):
        """Check the user's answer."""
        if not self.timer_running:
            return

        user_answer = self.answer_entry.get().strip()
        if not user_answer:
            messagebox.showinfo("Empty Answer", "Please enter an answer.")
            return

        # Stop timer
        self.timer_running = False

        correct_answer = self.current_question["answer"]

        # Check if answer is correct
        if isinstance(correct_answer, list):
            is_correct = user_answer.lower() in [a.lower() for a in correct_answer]
            display_answer = " or ".join(correct_answer)
        else:
            is_correct = user_answer.lower() == correct_answer.lower()
            display_answer = correct_answer

        if is_correct:
            # Calculate points based on time remaining and if hint was used
            points = max(1, int(self.remaining_time / 5))
            if self.hint_label.cget("text"):
                points = max(1, points // 2)  # Half points if hint was used

            self.score += points
            self.correct_answers += 1

            # Show success message with lab success theme
            self.result_label.config(
                text=f"✅ CORRECT! +{points} POINTS\n\n🔬 FUN FACT: {self.current_question['fact']}",
                fg=self.colors["success"],
                bg=self.colors["panel_bg"]
            )
        else:
            # Show failure message with lab error theme
            self.result_label.config(
                text=f"❌ INCORRECT!\nThe correct answer was: {display_answer}\n\n🔬 FUN FACT: {self.current_question['fact']}",
                fg=self.colors["error"],
                bg=self.colors["panel_bg"]
            )

        # Disable input
        self.answer_entry.config(state="disabled")
        self.hint_button.config(state="disabled")

        # Show next button with animation effect
        self.next_button.pack(pady=10)

        # Flash effect for the next button to draw attention
        def flash_button(count=0):
            if count < 6:  # Flash 3 times (6 color changes)
                if count % 2 == 0:
                    self.next_button.config(bg="#50CFFF")  # Lighter blue
                else:
                    self.next_button.config(bg=self.colors["neon_blue"])  # Original blue
                self.root.after(300, lambda: flash_button(count + 1))

        flash_button()

    def time_up(self):
        """Handle when time runs out for a question."""
        correct_answer = self.current_question["answer"]

        if isinstance(correct_answer, list):
            display_answer = " or ".join(correct_answer)
        else:
            display_answer = correct_answer

        # Show time up message with lab timeout theme
        self.result_label.config(
            text=f"⏱️ TIME'S UP!\nThe correct answer was: {display_answer}\n\n🔬 FUN FACT: {self.current_question['fact']}",
            fg=self.colors["error"],
            bg=self.colors["panel_bg"]
        )

        # Disable input
        self.answer_entry.config(state="disabled")
        self.hint_button.config(state="disabled")

        # Show next button with animation
        self.next_button.pack(pady=10)

        # Flash effect for the next button
        def flash_button(count=0):
            if count < 6:  # Flash 3 times (6 color changes)
                if count % 2 == 0:
                    self.next_button.config(bg="#50CFFF")  # Lighter blue
                else:
                    self.next_button.config(bg=self.colors["neon_blue"])  # Original blue
                self.root.after(300, lambda: flash_button(count + 1))

        flash_button()

    def show_results(self):
        """Show the results of the round."""
        # Hide game frame
        self.game_frame.pack_forget()

        # Clear results frame
        for widget in self.results_frame.winfo_children():
            widget.destroy()

        # Check for high score
        new_high_score = self.update_high_score()

        # Create a panel frame for content
        panel = tk.Frame(
            self.results_frame,
            bg=self.colors["panel_bg"],
            padx=40,
            pady=40,
            bd=0,
            highlightthickness=2,
            highlightbackground=self.colors["neon_green"]
        )

        # Title with lab completion theme
        title_frame = tk.Frame(panel, bg=self.colors["panel_bg"])
        title_frame.pack(pady=20)

        lab_icon = tk.Label(
            title_frame,
            text="🧪",
            font=("Arial", 36),
            bg=self.colors["panel_bg"],
            fg=self.colors["neon_green"]
        )
        lab_icon.pack(side="left", padx=(0, 10))

        title_label = tk.Label(
            title_frame,
            text="EXPERIMENT COMPLETE!",
            font=("Orbitron", 28, "bold"),
            bg=self.colors["panel_bg"],
            fg=self.colors["neon_green"]
        )
        title_label.pack(side="left")

        # Certificate-like frame
        certificate_frame = tk.Frame(
            panel,
            bg=self.colors["bg_dark"],
            padx=40,
            pady=40,
            highlightbackground=self.colors["neon_blue"],
            highlightthickness=2
        )
        certificate_frame.pack(pady=20, fill="x")

        # Certificate header
        cert_header = tk.Label(
            certificate_frame,
            text="CERTIFICATE OF ACHIEVEMENT",
            font=("Orbitron", 18, "bold"),
            bg=self.colors["bg_dark"],
            fg=self.colors["neon_blue"]
        )
        cert_header.pack(pady=(0, 20))

        # Results with atom icon
        results_text = f"You answered {self.correct_answers} out of {self.questions_in_round} questions correctly!"
        results_label = tk.Label(
            certificate_frame,
            text=results_text,
            font=("Orbitron", 16),
            bg=self.colors["bg_dark"],
            fg=self.colors["text_light"]
        )
        results_label.pack(pady=10)

        # Score with digital counter style
        score_frame = tk.Frame(
            certificate_frame,
            bg=self.colors["bg_dark"],
            highlightbackground=self.colors["neon_green"],
            highlightthickness=1,
            padx=20,
            pady=10
        )
        score_frame.pack(pady=20)

        score_text = f"FINAL SCORE: {self.score} POINTS"
        score_label = tk.Label(
            score_frame,
            text=score_text,
            font=("Orbitron", 20, "bold"),
            bg=self.colors["bg_dark"],
            fg=self.colors["neon_green"]
        )
        score_label.pack()

        # Stats section
        stats_frame = tk.Frame(certificate_frame, bg=self.colors["bg_dark"])
        stats_frame.pack(pady=20, fill="x")

        # Hints used
        hints_frame = tk.Frame(stats_frame, bg=self.colors["bg_dark"])
        hints_frame.pack(side="left", expand=True)

        hints_label = tk.Label(
            hints_frame,
            text="HINTS USED:",
            font=("Orbitron", 14),
            bg=self.colors["bg_dark"],
            fg=self.colors["warning"]
        )
        hints_label.pack()

        hints_value = tk.Label(
            hints_frame,
            text=str(self.hints_used),
            font=("Orbitron", 24, "bold"),
            bg=self.colors["bg_dark"],
            fg=self.colors["warning"]
        )
        hints_value.pack()

        # Difficulty
        difficulty_frame = tk.Frame(stats_frame, bg=self.colors["bg_dark"])
        difficulty_frame.pack(side="right", expand=True)

        difficulty_colors = {
            "easy": self.colors["neon_green"],
            "medium": self.colors["neon_blue"],
            "hard": self.colors["neon_purple"]
        }

        difficulty_label = tk.Label(
            difficulty_frame,
            text="DIFFICULTY:",
            font=("Orbitron", 14),
            bg=self.colors["bg_dark"],
            fg=difficulty_colors[self.current_difficulty]
        )
        difficulty_label.pack()

        difficulty_value = tk.Label(
            difficulty_frame,
            text=self.current_difficulty.upper(),
            font=("Orbitron", 24, "bold"),
            bg=self.colors["bg_dark"],
            fg=difficulty_colors[self.current_difficulty]
        )
        difficulty_value.pack()

        # High score section
        if new_high_score:
            high_score_frame = tk.Frame(
                certificate_frame,
                bg=self.colors["bg_dark"],
                highlightbackground=self.colors["neon_pink"],
                highlightthickness=2,
                padx=20,
                pady=10
            )
            high_score_frame.pack(pady=20, fill="x")

            high_score_label = tk.Label(
                high_score_frame,
                text=f"🏆 NEW HIGH SCORE FOR {self.current_difficulty.upper()} DIFFICULTY! 🏆",
                font=("Orbitron", 16, "bold"),
                bg=self.colors["bg_dark"],
                fg=self.colors["neon_pink"]
            )
            high_score_label.pack(pady=5)

        # Current high score
        current_high_score_label = tk.Label(
            certificate_frame,
            text=f"Current High Score: {self.high_scores[self.current_difficulty]} points",
            font=("Orbitron", 14),
            bg=self.colors["bg_dark"],
            fg=self.colors["text_light"]
        )
        current_high_score_label.pack(pady=10)

        # Button hover effect functions
        def on_enter(e, button, color):
            button.config(bg=color)

        def on_leave(e, button, color):
            button.config(bg=color)

        # Buttons
        buttons_frame = tk.Frame(panel, bg=self.colors["panel_bg"])
        buttons_frame.pack(pady=30)

        play_again_button = tk.Button(
            buttons_frame,
            text="PLAY AGAIN",
            command=self.start_game,
            font=("Orbitron", 14, "bold"),
            width=15,
            pady=10,
            bg=self.colors["neon_green"],
            activebackground=self.colors["neon_green"],
            fg=self.colors["bg_dark"],
            bd=0
        )
        play_again_button.grid(row=0, column=0, padx=10)
        play_again_button.bind("<Enter>", lambda e: on_enter(e, play_again_button, "#50FF50"))
        play_again_button.bind("<Leave>", lambda e: on_leave(e, play_again_button, self.colors["neon_green"]))

        main_menu_button = tk.Button(
            buttons_frame,
            text="MAIN MENU",
            command=self.show_main_menu,
            font=("Orbitron", 14, "bold"),
            width=15,
            pady=10,
            bg=self.colors["neon_blue"],
            activebackground=self.colors["neon_blue"],
            fg=self.colors["bg_dark"],
            bd=0
        )
        main_menu_button.grid(row=0, column=1, padx=10)
        main_menu_button.bind("<Enter>", lambda e: on_enter(e, main_menu_button, "#50CFFF"))
        main_menu_button.bind("<Leave>", lambda e: on_leave(e, main_menu_button, self.colors["neon_blue"]))

        # Center the panel in the frame
        panel.pack(expand=True, pady=50)

        # Show results frame
        self.results_frame.pack(fill="both", expand=True)

if __name__ == "__main__":
    root = tk.Tk()
    app = PeriodicTableQuestGUI(root)
    root.mainloop()
