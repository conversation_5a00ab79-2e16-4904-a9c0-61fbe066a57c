"""
Direct runner for the enhanced Periodic Table Quest game.
This ensures proper library detection and initialization.
"""

import sys
import os

def check_libraries():
    """Check and report library availability."""
    print("🔧 Checking Enhanced Features...")
    
    # Check PIL/Pillow
    try:
        from PIL import Image, ImageTk
        print("✅ PIL/Pillow: Available - Background images enabled")
        pil_ok = True
    except ImportError:
        print("❌ PIL/Pillow: Not available - Background images disabled")
        pil_ok = False
    
    # Check Pygame
    try:
        import pygame
        pygame.mixer.init()
        print("✅ Pygame: Available - Sound effects enabled")
        pygame_ok = True
    except ImportError:
        print("❌ Pygame: Not available - Sound effects disabled")
        pygame_ok = False
    except Exception as e:
        print(f"❌ Pygame: Initialization failed ({e}) - Sound effects disabled")
        pygame_ok = False
    
    return pil_ok, pygame_ok

def run_game():
    """Run the enhanced Periodic Table Quest game."""
    print("🧪 Starting Enhanced Periodic Table Quest...")
    print("=" * 50)
    
    # Check libraries
    pil_ok, pygame_ok = check_libraries()
    
    # Check for required files
    if not os.path.exists("periodic_quest_gui.py"):
        print("❌ Error: periodic_quest_gui.py not found!")
        return False
    
    if not os.path.exists("periodic_data.py"):
        print("❌ Error: periodic_data.py not found!")
        return False
    
    print("\n🎮 Launching game window...")
    
    # Import and run the game
    try:
        import tkinter as tk
        from periodic_quest_gui import PeriodicTableQuestGUI
        
        # Create the main window
        root = tk.Tk()
        
        # Initialize the game
        app = PeriodicTableQuestGUI(root)
        
        # Show feature status
        print(f"🎨 Background Image: {'Loaded' if app.background_image else 'Not loaded'}")
        print(f"🔊 Sound System: {'Initialized' if hasattr(app, 'sounds') else 'Not initialized'}")
        
        print("\n🚀 Game window opened! Enjoy your enhanced Periodic Table Quest!")
        print("   Close the game window to return to this terminal.")
        
        # Start the game loop
        root.mainloop()
        
        print("\n👋 Game closed. Thanks for playing!")
        return True
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error starting game: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Enhanced Periodic Table Quest Launcher")
    print("=" * 40)
    
    success = run_game()
    
    if not success:
        print("\n💡 Troubleshooting:")
        print("   • Make sure you're in the correct directory")
        print("   • Install missing libraries: pip install Pillow pygame")
        print("   • Check that all game files are present")
    
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
