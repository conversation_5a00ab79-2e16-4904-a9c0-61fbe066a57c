"""
Direct Exhibition Runner for Periodic Table Quest
This will run the enhanced version and keep it open for your exhibition.
"""

import tkinter as tk
from tkinter import messagebox
import subprocess
import sys
import os
import threading

class ExhibitionRunner:
    """Simple exhibition runner with status display."""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🧪 Periodic Table Quest - Exhibition Runner")
        self.root.geometry("600x400")
        self.root.configure(bg="#1a1a2e")
        
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the exhibition runner UI."""
        # Title
        title = tk.Label(
            self.root,
            text="🧪 PERIODIC TABLE QUEST\nEXHIBITION RUNNER",
            font=("Arial", 20, "bold"),
            bg="#1a1a2e",
            fg="#39ff14",
            justify="center"
        )
        title.pack(pady=30)
        
        # Status
        status = tk.Label(
            self.root,
            text="✅ EXHIBITION READY\nAll enhanced features available!",
            font=("Arial", 14),
            bg="#1a1a2e",
            fg="#00d4ff",
            justify="center"
        )
        status.pack(pady=20)
        
        # Buttons frame
        button_frame = tk.Frame(self.root, bg="#1a1a2e")
        button_frame.pack(pady=30)
        
        # Run working version button
        run_btn = tk.Button(
            button_frame,
            text="🚀 RUN ENHANCED EXHIBITION VERSION",
            command=self.run_exhibition,
            font=("Arial", 14, "bold"),
            bg="#39ff14",
            fg="white",
            padx=20,
            pady=15,
            bd=0
        )
        run_btn.pack(pady=10)
        
        # Run original enhanced button
        original_btn = tk.Button(
            button_frame,
            text="🎮 RUN ORIGINAL ENHANCED GAME",
            command=self.run_original,
            font=("Arial", 14, "bold"),
            bg="#00d4ff",
            fg="white",
            padx=20,
            pady=15,
            bd=0
        )
        original_btn.pack(pady=10)
        
        # Show demo button
        demo_btn = tk.Button(
            button_frame,
            text="🌐 SHOW WEB DEMO",
            command=self.show_demo,
            font=("Arial", 14, "bold"),
            bg="#bf00ff",
            fg="white",
            padx=20,
            pady=15,
            bd=0
        )
        demo_btn.pack(pady=10)
        
        # Features display
        features_text = """🎨 ENHANCED FEATURES ACTIVE:
• Animated floating atoms background
• Glowing interactive buttons
• Color-changing title animation
• Visual click effects
• Professional laboratory theme
• Complete periodic table quiz
• Enhanced scoring system"""
        
        features = tk.Label(
            self.root,
            text=features_text,
            font=("Arial", 11),
            bg="#0a0a0a",
            fg="#ffd700",
            justify="left",
            padx=20,
            pady=15
        )
        features.pack(pady=20, padx=20, fill="x")
        
        # Exit button
        exit_btn = tk.Button(
            self.root,
            text="🚪 EXIT",
            command=self.root.quit,
            font=("Arial", 12, "bold"),
            bg="#ff4444",
            fg="white",
            padx=20,
            pady=10,
            bd=0
        )
        exit_btn.pack(pady=20)
    
    def run_exhibition(self):
        """Run the working exhibition version."""
        try:
            print("🚀 Launching working exhibition version...")
            
            # Run in a separate thread to avoid blocking
            def launch():
                subprocess.run([sys.executable, "periodic_quest_working.py"])
            
            thread = threading.Thread(target=launch)
            thread.daemon = True
            thread.start()
            
            messagebox.showinfo("Exhibition Launched!", 
                              "🎉 Working Exhibition Version Launched!\n\n" +
                              "You should see a new window with:\n" +
                              "✨ Animated floating atoms\n" +
                              "🌈 Color-changing title\n" +
                              "💫 Glowing buttons\n" +
                              "⚡ Click animations\n\n" +
                              "Perfect for your exhibition demonstration!")
        except Exception as e:
            messagebox.showerror("Error", f"Could not launch exhibition version: {e}")
    
    def run_original(self):
        """Run the enhanced original game."""
        try:
            print("🎮 Launching enhanced original game...")
            
            def launch():
                subprocess.run([sys.executable, "periodic_quest_gui.py"])
            
            thread = threading.Thread(target=launch)
            thread.daemon = True
            thread.start()
            
            messagebox.showinfo("Enhanced Game Launched!", 
                              "🎮 Enhanced Original Game Launched!\n\n" +
                              "Features:\n" +
                              "🎨 Enhanced dark theme\n" +
                              "✨ Button animations\n" +
                              "🧪 Laboratory aesthetic\n" +
                              "🎯 Full quiz functionality\n" +
                              "📊 Scoring system\n\n" +
                              "Ready for your exhibition!")
        except Exception as e:
            messagebox.showerror("Error", f"Could not launch enhanced game: {e}")
    
    def show_demo(self):
        """Show the web demo."""
        try:
            import webbrowser
            demo_path = os.path.abspath("exhibition_demo.html")
            webbrowser.open(f"file://{demo_path}")
            messagebox.showinfo("Demo Opened!", 
                              "🌐 Web Demo Opened in Browser!\n\n" +
                              "This shows exactly what your\n" +
                              "enhanced Python app looks like!\n\n" +
                              "Use this as a visual reference\n" +
                              "for your exhibition presentation.")
        except Exception as e:
            messagebox.showerror("Error", f"Could not open demo: {e}")
    
    def run(self):
        """Run the exhibition runner."""
        print("🧪 EXHIBITION RUNNER STARTED")
        print("=" * 40)
        print("✅ Ready to launch enhanced versions")
        print("🎨 All visual enhancements available")
        print("🚀 Choose your exhibition option")
        print("=" * 40)
        
        self.root.mainloop()
        
        print("👋 Exhibition runner closed")

if __name__ == "__main__":
    runner = ExhibitionRunner()
    runner.run()
