"""
INSTANT EXHIBITION LAUNCHER
This script immediately opens the final enhanced Periodic Table Quest project.
No menus, no waiting - just instant launch of your exhibition!
"""

import subprocess
import sys
import os

def instant_launch():
    """Instantly launch the final exhibition project."""
    print("🧪 INSTANT EXHIBITION LAUNCH")
    print("🚀 Opening enhanced Periodic Table Quest NOW!")

    try:
        # Try the fixed version first (now with web demo style!)
        if os.path.exists("periodic_quest_fixed.py"):
            print("✅ Launching WEB DEMO STYLE exhibition version...")
            print("   🌐 Recreates the exact web demo appearance in Python!")
            subprocess.Popen([sys.executable, "periodic_quest_fixed.py"])
            return True

        # Try the web-style version (exact replica of web demo)
        elif os.path.exists("periodic_quest_web_style.py"):
            print("✅ Launching web-style exhibition version...")
            subprocess.Popen([sys.executable, "periodic_quest_web_style.py"])
            return True

        # Try the working exhibition version (guaranteed to work)
        elif os.path.exists("periodic_quest_working.py"):
            print("✅ Launching working exhibition version...")
            subprocess.Popen([sys.executable, "periodic_quest_working.py"])
            return True

        # Fallback to enhanced original
        elif os.path.exists("periodic_quest_gui.py"):
            print("✅ Launching enhanced original game...")
            subprocess.Popen([sys.executable, "periodic_quest_gui.py"])
            return True

        # Fallback to launcher
        elif os.path.exists("launch_exhibition.py"):
            print("✅ Launching exhibition launcher...")
            subprocess.Popen([sys.executable, "launch_exhibition.py"])
            return True

        else:
            print("❌ No exhibition files found!")
            return False

    except Exception as e:
        print(f"❌ Launch failed: {e}")
        return False

if __name__ == "__main__":
    success = instant_launch()
    if success:
        print("🎉 Exhibition launched successfully!")
    else:
        print("❌ Could not launch exhibition")
        input("Press Enter to exit...")
