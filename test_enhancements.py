"""
Test script for Periodic Table Quest enhancements.
This script tests the enhanced features without running the full GUI.
"""

import os
import sys

def test_imports():
    """Test if required libraries are available."""
    print("🧪 Testing Enhanced Features for Periodic Table Quest")
    print("=" * 50)

    results = {}

    # Test PIL/Pillow
    try:
        from PIL import Image, ImageTk
        results['PIL'] = "✅ Available"
        print("✅ PIL/Pillow: Available (Background images supported)")
    except ImportError:
        results['PIL'] = "❌ Missing"
        print("❌ PIL/Pillow: Missing (Install with: pip install Pillow)")

    # Test Pygame
    try:
        import pygame
        pygame.mixer.init()
        results['Pygame'] = "✅ Available"
        print("✅ Pygame: Available (Sound effects supported)")
    except ImportError:
        results['Pygame'] = "❌ Missing"
        print("❌ Pygame: Missing (Install with: pip install pygame)")
    except Exception as e:
        results['Pygame'] = f"❌ Error: {e}"
        print(f"❌ Pygame: Error initializing - {e}")

    # Test NumPy (for sample sound generation)
    try:
        import numpy
        results['NumPy'] = "✅ Available"
        print("✅ NumPy: Available (Sample sound generation supported)")
    except ImportError:
        results['NumPy'] = "❌ Missing"
        print("❌ NumPy: Missing (Install with: pip install numpy)")

    return results

def test_directory_structure():
    """Test if the asset directory structure exists."""
    print("\n📁 Testing Directory Structure")
    print("-" * 30)

    directories = [
        "assets",
        "assets/images",
        "assets/sounds"
    ]

    for directory in directories:
        if os.path.exists(directory):
            print(f"✅ {directory}: Exists")
        else:
            print(f"❌ {directory}: Missing")
            try:
                os.makedirs(directory, exist_ok=True)
                print(f"   → Created {directory}")
            except Exception as e:
                print(f"   → Error creating {directory}: {e}")

def test_asset_files():
    """Test if asset files exist and are valid."""
    print("\n🎨 Testing Asset Files")
    print("-" * 20)

    assets = {
        "Background Image": "assets/images/lab_bg.jpg",
        "Background Music": "assets/sounds/background_music.mp3",
        "Click Sound": "assets/sounds/click.wav",
        "Correct Sound": "assets/sounds/correct.wav",
        "Incorrect Sound": "assets/sounds/incorrect.wav",
        "Time Up Sound": "assets/sounds/time_up.wav"
    }

    found_assets = 0
    total_assets = len(assets)

    for name, path in assets.items():
        if os.path.exists(path):
            size = os.path.getsize(path)

            # Test if file is valid
            valid = True
            try:
                if path.endswith(('.jpg', '.png', '.gif')):
                    # Test image file
                    try:
                        from PIL import Image
                        with Image.open(path) as img:
                            width, height = img.size
                        print(f"✅ {name}: Found ({size:,} bytes, {width}x{height})")
                    except ImportError:
                        print(f"✅ {name}: Found ({size:,} bytes)")
                elif path.endswith(('.wav', '.mp3', '.ogg')):
                    # Test audio file
                    try:
                        import pygame
                        pygame.mixer.init()
                        sound = pygame.mixer.Sound(path)
                        duration = sound.get_length()
                        print(f"✅ {name}: Found ({size:,} bytes, {duration:.1f}s)")
                    except ImportError:
                        print(f"✅ {name}: Found ({size:,} bytes)")
                    except Exception as e:
                        print(f"⚠️ {name}: Found but may be invalid ({size:,} bytes) - {e}")
                        valid = False
                else:
                    print(f"✅ {name}: Found ({size:,} bytes)")
            except Exception as e:
                print(f"⚠️ {name}: Found but error reading ({size:,} bytes) - {e}")
                valid = False

            if valid:
                found_assets += 1
        else:
            print(f"❌ {name}: Missing ({path})")

    print(f"\n📊 Assets Summary: {found_assets}/{total_assets} files found and valid")

    if found_assets == 0:
        print("\n💡 Tip: Run 'python create_all_assets.py' to create all assets")
        print("   Or run 'python create_simple_sounds.py' for just sound files")
    elif found_assets < total_assets:
        print(f"\n💡 {total_assets - found_assets} assets missing or invalid")
        print("   Run 'python create_all_assets.py' to create missing assets")
    else:
        print("\n🎉 All assets found and valid!")

    return found_assets, total_assets

def test_enhanced_gui_import():
    """Test if the enhanced GUI can be imported."""
    print("\n🖥️ Testing Enhanced GUI Import")
    print("-" * 30)

    try:
        # Add current directory to path
        sys.path.insert(0, '.')

        # Try to import the enhanced GUI
        from periodic_quest_gui import PeriodicTableQuestGUI
        print("✅ Enhanced GUI: Import successful")

        # Test if the new methods exist
        methods_to_check = [
            'setup_assets',
            'setup_sounds',
            'play_sound',
            'setup_background',
            'create_enhanced_button',
            'lighten_color'
        ]

        for method in methods_to_check:
            if hasattr(PeriodicTableQuestGUI, method):
                print(f"✅ Method {method}: Available")
            else:
                print(f"❌ Method {method}: Missing")

        return True

    except ImportError as e:
        print(f"❌ Enhanced GUI: Import failed - {e}")
        return False
    except Exception as e:
        print(f"❌ Enhanced GUI: Error - {e}")
        return False

def test_periodic_data():
    """Test if periodic data is available."""
    print("\n⚛️ Testing Periodic Data")
    print("-" * 25)

    try:
        from periodic_data import ELEMENTS, DIFFICULTY_LEVELS, ELEMENT_GROUPS
        print(f"✅ Elements: {len(ELEMENTS)} elements loaded")
        print(f"✅ Difficulty Levels: {list(DIFFICULTY_LEVELS.keys())}")
        print(f"✅ Element Groups: {len(ELEMENT_GROUPS)} groups loaded")
        return True
    except ImportError as e:
        print(f"❌ Periodic Data: Import failed - {e}")
        return False

def generate_report(results, found_assets=0, total_assets=6):
    """Generate a summary report."""
    print("\n" + "=" * 50)
    print("📋 ENHANCEMENT TEST REPORT")
    print("=" * 50)

    # Count successful tests
    pil_ok = "✅" in str(results.get('PIL', ''))
    pygame_ok = "✅" in str(results.get('Pygame', ''))
    numpy_ok = "✅" in str(results.get('NumPy', ''))

    print(f"🎨 Background Images: {'Supported' if pil_ok else 'Not Supported'}")
    print(f"🔊 Sound Effects: {'Supported' if pygame_ok else 'Not Supported'}")
    print(f"🎵 Sample Generation: {'Supported' if numpy_ok else 'Not Supported'}")
    print(f"📁 Asset Files: {found_assets}/{total_assets} found")

    # Asset status
    if found_assets == total_assets:
        print("✅ All assets available!")
    elif found_assets > 0:
        print(f"⚠️ {total_assets - found_assets} assets missing")
    else:
        print("❌ No assets found")

    # Recommendations
    print("\n💡 RECOMMENDATIONS:")

    if not pil_ok:
        print("   • Install Pillow for background images: pip install Pillow")

    if not pygame_ok:
        print("   • Install Pygame for sound effects: pip install pygame")

    if not numpy_ok:
        print("   • Install NumPy for sample generation: pip install numpy")

    if found_assets < total_assets:
        print("   • Create missing assets: python create_all_assets.py")
        print("   • Or create just sounds: python create_simple_sounds.py")

    if pil_ok and pygame_ok and found_assets == total_assets:
        print("   • ALL ENHANCED FEATURES ARE READY! 🎉")
        print("   • Run 'python periodic_quest_gui.py' to start the enhanced game")
        print("   • Run 'python launch_exhibition.py' for exhibition launcher")
    elif pil_ok and pygame_ok:
        print("   • Enhanced features supported! Create assets to complete setup 🎉")
        print("   • Run 'python periodic_quest_gui.py' to start the enhanced game")

    print("\n🚀 Ready to enhance your Periodic Table Quest experience!")

def main():
    """Run all tests."""
    # Test imports
    results = test_imports()

    # Test directory structure
    test_directory_structure()

    # Test asset files
    found_assets, total_assets = test_asset_files()

    # Test GUI import
    gui_ok = test_enhanced_gui_import()

    # Test periodic data
    data_ok = test_periodic_data()

    # Generate report
    generate_report(results, found_assets, total_assets)

    # Final status
    if gui_ok and data_ok and found_assets == total_assets:
        print("\n🎯 Status: Ready to run enhanced game with all features!")
        return True
    elif gui_ok and data_ok:
        print("\n🎯 Status: Ready to run enhanced game (some assets missing)")
        return True
    else:
        print("\n⚠️ Status: Some issues found. Check messages above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
